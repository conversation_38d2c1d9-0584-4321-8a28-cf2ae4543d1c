<?php if(in_array(config('app.locale'),['ar','he','fa','ur'])): ?>
<nav class="navbar navbar-vertical fixed-right navbar-expand-md navbar-light bg-white" id="sidenav-main">
    <?php else: ?>
    <nav class="navbar navbar-vertical fixed-left navbar-expand-md navbar-light bg-white" id="sidenav-main">
        <?php endif; ?>

        <div class="container-fluid">
            <!-- Toggler -->
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#sidenav-collapse-main"
                aria-controls="sidenav-main" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <!-- Brand -->
            <a class="navbar-brand pt-0" href="/">
                <img src="<?php echo e(config('settings.logo')); ?>" class="navbar-brand-img" alt="...">
            </a>
            <!-- User -->
            <ul class="nav align-items-center d-md-none">
                <li class="nav-item dropdown">
                    <a class="nav-link" href="#" role="button" data-toggle="dropdown" aria-haspopup="true"
                        aria-expanded="false">
                        <div class="media align-items-center">
                            <span class="avatar avatar-sm rounded-circle">


                                <img alt="..." src="<?php echo e('https://www.gravatar.com/avatar/'.md5(auth()->user()->email)); ?>">
                            </span>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-arrow dropdown-menu-right">
                        <div class=" dropdown-header noti-title">
                            <h6 class="text-overflow m-0"><?php echo e(__('Welcome!')); ?></h6>
                        </div>
                        <a href="<?php echo e(route('profile.show')); ?>" class="dropdown-item">
                            <i class="ni ni-single-02"></i>
                            <span><?php echo e(__('My profile')); ?></span>
                        </a>
                        <?php if(config('settings.app_code_name','')=="wpbox"&&auth()->user()->hasRole('owner')): ?>
                        <a href="<?php echo e(route('whatsapp.setup')); ?>" class="dropdown-item">
                            <i class="ni ni-support-16"></i>
                            <span><?php echo e(__('Whatsapp Cloud API Setup')); ?></span>
                        </a>
                        <?php endif; ?>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo e(route('logout')); ?>" class="dropdown-item"
                            onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="ni ni-user-run"></i>
                            <span><?php echo e(__('Logout')); ?></span>
                        </a>
                    </div>
                </li>
            </ul>
            <!-- Collapse -->
            <div class="collapse navbar-collapse" id="sidenav-collapse-main">
                <!-- Collapse header -->
                <div class="navbar-collapse-header d-md-none">
                    <div class="row">
                        <div class="col-6 collapse-brand">
                            <a href="<?php echo e(route('dashboard')); ?>">
                                <img src="<?php echo e(config('global.site_logo')); ?>">
                            </a>
                        </div>
                        <div class="col-6 collapse-close">
                            <button type="button" class="navbar-toggler" data-toggle="collapse"
                                data-target="#sidenav-collapse-main" aria-controls="sidenav-main" aria-expanded="false"
                                aria-label="Toggle sidenav">
                                <span></span>
                                <span></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Organization switcher -->
                <?php if(config('settings.enable_multi_organizations',true) && auth()->user()->hasRole('owner')): ?>
                    <h5 class="text-uppercase mt-3 mb-2 text-muted text-xs font-weight-bolder">
                        <?php echo e(__('Organization')); ?>

                    </h5>
                    <div class="dropdown w-100 mb-4">
                        <button class="btn btn-default dropdown-toggle w-100 text-left" type="button" id="orgDropdown"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            🏢
                            <span><?php echo e(auth()->user()->currentCompany()->name); ?></span>
                        </button>
                        <div class="dropdown-menu w-100" aria-labelledby="orgDropdown">
                            
                            <?php $__currentLoopData = auth()->user()->companies->where('active', 1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a class="dropdown-item"
                                href="<?php echo e(route('admin.companies.switch', $company->id)); ?>">
                                <div class="d-flex align-items-center">
                                    <span><?php echo e($company->name); ?></span>
                                </div>
                            </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('admin.organizations.manage')); ?>" class="dropdown-item">
                                <span>🏢 <?php echo e(__('Organizations')); ?></span>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
                <!-- Navigation -->
                <?php if(Auth::user()->isImpersonating()): ?>
                <hr class="my-3">
                <ul class="navbar-nav ">
                    <li class="nav-item">
                        <a class="nav-link active active-pro" href="<?php echo e(route('admin.companies.stopImpersonate')); ?>">
                            <i class="ni ni-button-power text-red"></i>
                            <span class="nav-link-text"><?php echo e(__('Back to your account')); ?></span>
                        </a>
                    </li>
                </ul>
                <hr class="my-3">
                <?php endif; ?>
                <?php if(auth()->user()->hasRole('admin')): ?>
                <?php echo $__env->make('admin.navbars.menus.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php else: ?>
                <span></span>
                <?php endif; ?>



                <?php if(auth()->user()->hasRole('owner')): ?>
                <?php echo $__env->make('admin.navbars.menus.owner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php else: ?>
                <span></span>
                <?php endif; ?>

                <?php if(auth()->user()->hasRole('staff')): ?>
                <?php echo $__env->make('admin.navbars.menus.staff', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php else: ?>
                <span></span>
                <?php endif; ?>

                <?php if(auth()->user()->hasRole('client')): ?>
                <?php echo $__env->make('admin.navbars.menus.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php else: ?>
                <span></span>
                <?php endif; ?>
                
                
                <div id="update_notification" style="display:none;" class="alert alert-info sticky-top">
                    <button type="button" style="margin-left: 20px" class="close" data-dismiss="alert"
                        aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div id="uptodate_notification" style="display:none;" class="alert alert-success sticky-top">
                    <button type="button" style="margin-left: 20px" class="close" data-dismiss="alert"
                        aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

            </div>
        </div>
    </nav>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/admin/navbars/sidebar.blade.php ENDPATH**/ ?>
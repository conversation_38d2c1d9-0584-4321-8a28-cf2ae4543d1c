<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        
        <?php echo $__env->yieldContent('title'); ?>
        <title><?php echo e(config('app.name', 'Site')); ?></title>

         <!-- Fonts -->
         <link rel="preconnect" href="https://fonts.bunny.net">
         <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />


        
        <!-- Icons -->
        <?php if(config('settings.icon_type','nucleo') == 'hero'): ?>   
            <link href="<?php echo e(asset('vendor/argon')); ?>/vendor/hero/css/hero.css" rel="stylesheet">
        <?php else: ?>
            <link href="<?php echo e(asset('vendor/argon')); ?>/vendor/nucleo/css/nucleo.css" rel="stylesheet">
        <?php endif; ?>
        
        
        <link type="text/css" href="<?php echo e(asset('vendor/argon')); ?>/css/argon.css?v=1.0.0" rel="stylesheet">
        <link rel="stylesheet" href="<?php echo e(asset('vendor')); ?>/jasny/css/jasny-bootstrap.min.css">
   

        <?php echo $__env->yieldContent('head'); ?>

        <?php echo $__env->make('layouts.rtl', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- Custom CSS defined by admin -->
        <link type="text/css" href="<?php echo e(asset('byadmin')); ?>/back.css" rel="stylesheet">

        <!-- Select2  -->
        <link type="text/css" href="<?php echo e(asset('vendor')); ?>/select2/select2.min.css" rel="stylesheet">

        <!-- Custom CSS defined by user -->
        <link type="text/css" href="<?php echo e(asset('custom')); ?>/css/custom.css?id=<?php echo e(config('version.version')); ?>" rel="stylesheet">

        <!-- Flags -->
        <link type="text/css" href="<?php echo e(asset('vendor')); ?>/flag-icons/css/flag-icons.min.css" rel="stylesheet" />

        <!-- Bootstap VUE -->
        <link type="text/css" href="<?php echo e(asset('vendor')); ?>/vue/bootstrap-vue.css" rel="stylesheet" />

        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">


    </head>
    <body class="<?php echo e($class ?? ''); ?>">
        <?php if(auth()->guard()->check()): ?>
            <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                <?php echo csrf_field(); ?>
            </form>
            <?php echo $__env->make('admin.navbars.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <div class="main-content">
            <?php echo $__env->make('admin.navbars.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->yieldContent('content'); ?>
        </div>

        <?php if(auth()->guard()->guest()): ?>
           
        <?php endif; ?>

            <!-- Commented because navtabs includes same script -->
           

            <?php echo $__env->yieldContent('topjs'); ?>
    
            <script>
                var t="<?php echo 'translations'.App::getLocale() ?>";
               window.translations = <?php echo Cache::get('translations'.App::getLocale(),"[]"); ?>;
               
               
            </script>
            
            <!-- Navtabs -->
            <script src="<?php echo e(asset('vendor')); ?>/jquery/jquery.min.js" type="text/javascript"></script>
            <script src="<?php echo e(asset('vendor/argon')); ?>/js/popper.min.js" type="text/javascript"></script>
            

            <script src="<?php echo e(asset('vendor/argon')); ?>/vendor/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
    
            <!-- Nouslider -->
            <script src="<?php echo e(asset('vendor/argon')); ?>/vendor/nouislider/distribute/nouislider.min.js" type="text/javascript"></script>
    
            <!-- Latest compiled and minified JavaScript -->
            <script src="<?php echo e(asset('vendor')); ?>/jasny/js/jasny-bootstrap.min.js"></script>
    
   
            <!-- All in one -->
            <script src="<?php echo e(asset('custom')); ?>/js/js.js?id=<?php echo e(config('version.version')); ?>"></script>

            <!-- Notify JS -->
            <script src="<?php echo e(asset('custom')); ?>/js/notify.min.js"></script>
    
            <!-- Argon JS -->
            <script src="<?php echo e(asset('vendor/argon')); ?>/js/argon.js?v=1.0.0"></script>

    
    
            <script>
                var ONESIGNAL_APP_ID = "<?php echo e(config('settings.onesignal_app_id')); ?>";
                var USER_ID = '<?php echo e(auth()->user()&&auth()->user()?auth()->user()->id:""); ?>';
                var PUSHER_APP_KEY = "<?php echo e(config('broadcasting.connections.pusher.key')); ?>";
               
                var PUSHER_APP_CLUSTER = "<?php echo e(config('broadcasting.connections.pusher.options.cluster')); ?>";
            </script>
            <?php if(auth()->user()!=null&&auth()->user()->hasRole('staff')): ?>
                <script>
                    //When staff, use the owner
                    USER_ID = '<?php echo e(auth()->user()->company->user_id); ?>';
                </script>
            <?php endif; ?>
           
    
            <!-- OneSignal -->
            <?php if(strlen( config('settings.onesignal_app_id'))>4): ?>
                <script src="<?php echo e(asset('vendor')); ?>/OneSignalSDK/OneSignalSDK.js" async=""></script>
                <script src="<?php echo e(asset('custom')); ?>/js/onesignal.js"></script>
            <?php endif; ?>
    
            <?php echo $__env->yieldPushContent('js'); ?>
            <?php echo $__env->yieldContent('js'); ?>
    

    
             <!-- Pusher -->
             <?php if(strlen(config('broadcasting.connections.pusher.app_id'))>2): ?>
                <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
                <?php if(config('settings.app_code_name','')=="qrpdf"): ?>
                    <script src="<?php echo e(asset('custom')); ?>/js/pusher.js"></script>    
                <?php endif; ?>
            <?php endif; ?>

            <!-- Import Select2 --->
            <script src="<?php echo e(asset('vendor')); ?>/select2/select2.min.js"></script>
    
            <!-- Custom JS defined by admin -->
            <script src="<?php echo e(asset('byadmin')); ?>/back.js"></script>

            <!-- Import Moment -->
            <script type="text/javascript" src="<?php echo e(asset('vendor')); ?>/moment/moment.min.js"></script>
            <script type="text/javascript" src="<?php echo e(asset('vendor')); ?>/moment/momenttz.min.js"></script>
            <script src="<?php echo e(asset('vendor/argon')); ?>/js/bootstrap.min.js" type="text/javascript"></script>
            
            <!-- Import Vue -->
            <script src="<?php echo e(asset('vendor')); ?>/vue/vue.js"></script>
            <script src="<?php echo e(asset('vendor')); ?>/vue/bootstrap-vue.min.js"></script> 
            
           
             
            <!-- Import AXIOS --->
            <script src="<?php echo e(asset('vendor')); ?>/axios/axios.min.js"></script>

            <?php 
                echo file_get_contents(base_path('public/byadmin/back.js')) 
            ?>
    </body>
</html>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/admin/app_bs.blade.php ENDPATH**/ ?>
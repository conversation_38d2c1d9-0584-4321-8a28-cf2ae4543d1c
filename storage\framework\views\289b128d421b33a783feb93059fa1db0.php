<?php if(auth()->guard()->check()): ?>
   
    <?php if(!isset($hideActions)): ?>
        <?php echo $__env->make('admin.navbars.navs.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
        

<?php endif; ?>

<?php if(auth()->guard()->guest()): ?>
    <?php if(\Request::route()->getName() != "order.success"): ?>
        <?php echo $__env->make('admin.navbars.navs.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/admin/navbars/navbar.blade.php ENDPATH**/ ?>
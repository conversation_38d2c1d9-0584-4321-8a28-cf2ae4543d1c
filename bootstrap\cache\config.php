<?php return array (
  'app' => 
  array (
    'name' => 'Zaptra',
    'env' => 'production',
    'debug' => false,
    'url' => 'http://localhost/zaptra/public',
    'asset_url' => NULL,
    'ignore_subdomains' => 
    array (
      0 => 'demo',
    ),
    'framework' => 'bootstrap',
    'images_upload_path' => 'uploads/companies/',
    'timezone' => 'Asia/Kolkata',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'key' => 'base64:WDFcRRDEwEcJ0ogSCdn6+EVJRUX93kUEgG8qRgZEYhw=',
    'cipher' => 'AES-256-CBC',
    'maintenance' => 
    array (
      'driver' => 'file',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      15 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      16 => 'Illuminate\\Queue\\QueueServiceProvider',
      17 => 'Illuminate\\Redis\\RedisServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'Akaunting\\Money\\Provider',
      23 => 'App\\Providers\\AppServiceProvider',
      24 => 'App\\Providers\\AuthServiceProvider',
      25 => 'App\\Providers\\EventServiceProvider',
      26 => 'App\\Providers\\RouteServiceProvider',
      27 => 'App\\Providers\\TelescopeServiceProvider',
      28 => 'App\\Providers\\FortifyServiceProvider',
      29 => 'App\\Providers\\JetstreamServiceProvider',
      30 => 'App\\Providers\\TranslationServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
      'Image' => 'Intervention\\Image\\ImageManagerStatic',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'broadcasting' => 
  array (
    'default' => 'pusher',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '6c8152eb9aeec53e8afd',
        'secret' => '85b1ec5896702f8ac767',
        'app_id' => '2013266',
        'options' => 
        array (
          'cluster' => 'ap2',
          'host' => 'api-ap2.pusher.com',
          'port' => 443,
          'scheme' => 'https',
          'encrypted' => true,
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
        'lock_connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\zaptra\\storage\\framework/cache/data',
        'lock_path' => 'C:\\xampp\\htdocs\\zaptra\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'zaptra_cache_',
  ),
  'config' => 
  array (
    'env' => 
    array (
      0 => 
      array (
        'name' => 'Setup',
        'slug' => 'setup',
        'icon' => 'ni ni-settings',
        'fields' => 
        array (
          0 => 
          array (
            'separator' => '🖥️ System',
            'title' => 'Project name',
            'key' => 'APP_NAME',
            'value' => 'Site name',
          ),
          1 => 
          array (
            'title' => 'Link to your site',
            'key' => 'APP_URL',
            'value' => 'http://localhost',
          ),
          2 => 
          array (
            'title' => 'Subdomains',
            'key' => 'IGNORE_SUBDOMAINS',
            'value' => 'www,127',
            'help' => 'Subdomain your app works in. ex if your subdomain is app.yourdomain.com, here you should have www,app ',
          ),
          3 => 
          array (
            'title' => '🚨 App debugging',
            'key' => 'APP_DEBUG',
            'value' => 'true',
            'ftype' => 'bool',
            'help' => 'Enable if you experience error 500',
          ),
          4 => 
          array (
            'title' => 'Disable the landing page',
            'help' => 'When landing page is disabled, the project will start from the login page. In this case it is best to have the system in subdomain',
            'key' => 'DISABLE_LANDING',
            'value' => 'false',
            'ftype' => 'bool',
          ),
          5 => 
          array (
            'title' => 'Wildcard domain',
            'help' => 'If you have followed the procedure to enable wildcard domain, select this so you can have shopname.yourdomain.com',
            'key' => 'WILDCARD_DOMAIN_READY',
            'value' => 'false',
            'ftype' => 'bool',
          ),
          6 => 
          array (
            'title' => 'Pagination count',
            'key' => 'PAGINATE_COUNT',
            'value' => 10,
            'ftype' => 'input',
            'type' => 'number',
          ),
          7 => 
          array (
            'separator' => '☁️ Storage settings',
            'title' => 'File system for storage',
            'help' => 'Where the system will store the media - images, videos, sounds.',
            'key' => 'STORAGE_TYPE',
            'value' => 'public_uploads',
            'ftype' => 'select',
            'data' => 
            array (
              'public_uploads' => 'Local filesystem',
              's3' => 'Amazon S3 | DigitalOcean Spaces',
            ),
          ),
          8 => 
          array (
            'title' => 'S3 AWS_ACCESS_KEY',
            'key' => 'AWS_ACCESS_KEY_ID',
            'value' => '',
          ),
          9 => 
          array (
            'title' => 'S3 AWS_SECRET_ACCESS_KEY',
            'key' => 'AWS_SECRET_ACCESS_KEY',
            'value' => '',
          ),
          10 => 
          array (
            'title' => 'S3 AWS_DEFAULT_REGION',
            'key' => 'AWS_DEFAULT_REGION',
            'value' => '',
          ),
          11 => 
          array (
            'title' => 'S3 AWS_BUCKET',
            'key' => 'AWS_BUCKET',
            'value' => '',
          ),
          12 => 
          array (
            'separator' => '🔐 Login services',
            'title' => 'Google client id for sign in',
            'key' => 'GOOGLE_CLIENT_ID',
            'value' => '',
          ),
          13 => 
          array (
            'title' => 'Google client secret for sign in',
            'key' => 'GOOGLE_CLIENT_SECRET',
            'value' => '',
          ),
          14 => 
          array (
            'title' => 'Google redirect link for sign in',
            'key' => 'GOOGLE_REDIRECT',
            'value' => '',
          ),
          15 => 
          array (
            'title' => 'Facebook client id',
            'key' => 'FACEBOOK_CLIENT_ID',
            'value' => '',
          ),
          16 => 
          array (
            'title' => 'Facebook client secret',
            'key' => 'FACEBOOK_CLIENT_SECRET',
            'value' => '',
          ),
          17 => 
          array (
            'title' => 'Facebook redirect',
            'key' => 'FACEBOOK_REDIRECT',
            'value' => '',
          ),
          18 => 
          array (
            'title' => 'Facebook app id (ES)',
            '' => 'Used for WhatsApp Signup',
            'key' => 'FACEBOOK_APP_ID',
            'value' => '',
          ),
          19 => 
          array (
            'title' => 'Facebook app secret (ES)',
            'key' => 'FACEBOOK_APP_SECRET',
            'value' => '',
          ),
          20 => 
          array (
            'title' => 'Facebook config id (ES)',
            '' => 'Used for WhatsApp Embeded Signup',
            'key' => 'FACEBOOK_CONFIG_ID',
            'value' => '',
          ),
          21 => 
          array (
            'separator' => 'Other settings',
            'title' => 'Enable Multiple Organizations',
            'key' => 'ENABLE_MULTI_ORGANIZATIONS',
            'value' => 'true',
            'ftype' => 'bool',
          ),
          22 => 
          array (
            'title' => 'Vendor entity name',
            'help' => 'Ex. Company, Company, Shop, Business etc',
            'key' => 'VENDOR_ENTITY_NAME',
            'value' => 'Company',
          ),
          23 => 
          array (
            'title' => 'Vendor entity name in plural',
            'help' => 'Ex. Companies, Companies, Shops, Businesses etc',
            'key' => 'VENDOR_ENTITY_NAME_PLURAL',
            'value' => 'Companies',
          ),
          24 => 
          array (
            'title' => 'Url route for vendor',
            'help' => 'If you want to change the link the vendor is open in. ex yourdomain.com/shop/shopname. shop - should be the value here',
            'key' => 'URL_ROUTE',
            'value' => 'company',
            'hideon' => 'wpbox',
          ),
          25 => 
          array (
            'title' => 'Url route for vendor in plural',
            'help' => 'If you want to change the link the vendor management is open in. ex yourdomain.com/shops. shops - should be the value here',
            'key' => 'URL_ROUTE_PLURAL',
            'value' => 'companies',
            'hideon' => 'wpbox',
          ),
          26 => 
          array (
            'title' => 'Demo vendor slug',
            'help' => 'Enter the domain - slug of your demo vendor that will show on the landing page',
            'key' => 'demo_company_slug',
            'value' => 'leukapizza',
            'onlyin' => 'qrsaas',
          ),
          27 => 
          array (
            'title' => 'Apps download code',
            'help' => 'If you have extended license, or some specific product, we will send you App download code. Send us ticket.',
            'key' => 'EXTENDED_LICENSE_DOWNLOAD_CODE',
            'value' => '',
          ),
          28 => 
          array (
            'title' => 'App environment',
            'key' => 'APP_ENV',
            'value' => 'local',
            'ftype' => 'select',
            'data' => 
            array (
              'local' => 'Local',
              'prodcution' => 'Production',
            ),
          ),
          29 => 
          array (
            'title' => 'Debug app level',
            'type' => 'hidden',
            'key' => 'APP_LOG_LEVEL',
            'value' => 'debug',
            'data' => 
            array (
              'debug' => 'Debug',
              'error' => 'Error',
            ),
          ),
        ),
      ),
      1 => 
      array (
        'name' => 'Finances',
        'slug' => 'finances',
        'icon' => 'ni ni-money-coins',
        'fields' => 
        array (
          0 => 
          array (
            'separator' => 'General',
            'title' => 'Tool used for subscriptions',
            'key' => 'SUBSCRIPTION_PROCESSOR',
            'value' => 'Stripe',
            'ftype' => 'select',
            'data' => 
            array (
              'Stripe' => 'Stripe',
              'Local' => 'Local bank transfers',
              'Razorpay' => 'Razorpay',
              'Stripeh' => 'Stripeh',
            ),
          ),
          1 => 
          array (
            'title' => 'Enable Pricing',
            'key' => 'ENABLE_PRICING',
            'value' => 'true',
            'ftype' => 'bool',
          ),
          2 => 
          array (
            'title' => 'The free plan ID',
            'key' => 'FREE_PRICING_ID',
            'value' => '1',
          ),
          3 => 
          array (
            'title' => 'Force users to use paid plan',
            'key' => 'FORCE_USERS_TO_PAY',
            'value' => 'false',
            'ftype' => 'bool',
          ),
          4 => 
          array (
            'separator' => 'Credits System',
            'title' => 'Enable Credits System',
            'key' => 'ENABLE_CREDITS',
            'value' => 'true',
            'ftype' => 'bool',
          ),
          5 => 
          array (
            'separator' => 'Stripe',
            'title' => 'Stripe API key',
            'key' => 'STRIPE_KEY',
            'value' => 'pk_test_XXXXXXXXXXXXXX',
          ),
          6 => 
          array (
            'title' => 'Stripe API Secret',
            'key' => 'STRIPE_SECRET',
            'value' => 'sk_test_XXXXXXXXXXXXXXX',
          ),
          7 => 
          array (
            'separator' => 'Local bank transfer',
            'title' => 'Local bank transfer explanation',
            'key' => 'LOCAL_TRANSFER_INFO',
            'value' => 'Wire us the plan amount on the following bank account. And inform us about the wire.',
          ),
          8 => 
          array (
            'title' => 'Bank Account',
            'key' => 'LOCAL_TRANSFER_ACCOUNT',
            'value' => 'IBAN: **************',
          ),
        ),
      ),
      2 => 
      array (
        'name' => 'Localization',
        'slug' => 'localizatino',
        'icon' => 'ni ni-world-2',
        'fields' => 
        array (
          0 => 
          array (
            'title' => 'Default language',
            'help' => 'If you make change, make sure you first have added the new language in Translations and you have done the translations.',
            'key' => 'APP_LOCALE',
            'value' => 'en',
            'ftype' => 'select',
            'data' => 
            array (
              'af' => 'Afrikaans',
              'ak' => 'Akan',
              'sq' => 'Albanian',
              'am' => 'Amharic',
              'ar' => 'Arabic',
              'hy' => 'Armenian',
              'as' => 'Assamese',
              'az' => 'Azerbaijani',
              'bm' => 'Bambara',
              'eu' => 'Basque',
              'be' => 'Belarusian',
              'bn' => 'Bengali',
              'bs' => 'Bosnian',
              'bg' => 'Bulgarian',
              'my' => 'Burmese',
              'ca' => 'Catalan',
              'zh' => 'Chinese',
              'kw' => 'Cornish',
              'hr' => 'Croatian',
              'cs' => 'Czech',
              'da' => 'Danish',
              'nl' => 'Dutch',
              'en' => 'English',
              'eo' => 'Esperanto',
              'et' => 'Estonian',
              'ee' => 'Ewe',
              'fo' => 'Faroese',
              'fi' => 'Finnish',
              'fr' => 'French',
              'ff' => 'Fulah',
              'gl' => 'Galician',
              'lg' => 'Ganda',
              'ka' => 'Georgian',
              'de' => 'German',
              'el' => 'Greek',
              'gu' => 'Gujarati',
              'ha' => 'Hausa',
              'he' => 'Hebrew',
              'hi' => 'Hindi',
              'hu' => 'Hungarian',
              'is' => 'Icelandic',
              'ig' => 'Igbo',
              'id' => 'Indonesian',
              'ga' => 'Irish',
              'it' => 'Italian',
              'ja' => 'Japanese',
              'kl' => 'Kalaallisut',
              'kn' => 'Kannada',
              'kk' => 'Kazakh',
              'km' => 'Khmer',
              'ki' => 'Kikuyu',
              'rw' => 'Kinyarwanda',
              'ko' => 'Korean',
              'lv' => 'Latvian',
              'lt' => 'Lithuanian',
              'mk' => 'Macedonian',
              'mg' => 'Malagasy',
              'ms' => 'Malay',
              'ml' => 'Malayalam',
              'mt' => 'Maltese',
              'gv' => 'Manx',
              'mr' => 'Marathi',
              'ne' => 'Nepali',
              'nd' => 'North Ndebele',
              'nb' => 'Norwegian Bokmål',
              'nn' => 'Norwegian Nynorsk',
              'or' => 'Oriya',
              'om' => 'Oromo',
              'ps' => 'Pashto',
              'fa' => 'Persian',
              'pl' => 'Polish',
              'pt' => 'Portuguese',
              'pa' => 'Punjabi',
              'ro' => 'Romanian',
              'rm' => 'Romansh',
              'ru' => 'Russian',
              'sg' => 'Sango',
              'sr' => 'Serbian',
              'sn' => 'Shona',
              'ii' => 'Sichuan Yi',
              'si' => 'Sinhala',
              'sk' => 'Slovak',
              'sl' => 'Slovenian',
              'so' => 'Somali',
              'es' => 'Spanish',
              'sw' => 'Swahili',
              'sv' => 'Swedish',
              'ta' => 'Tamil',
              'te' => 'Telugu',
              'th' => 'Thai',
              'bo' => 'Tibetan',
              'ti' => 'Tigrinya',
              'to' => 'Tonga',
              'tr' => 'Turkish',
              'uk' => 'Ukrainian',
              'ur' => 'Urdu',
              'uz' => 'Uzbek',
              'vi' => 'Vietnamese',
              'cy' => 'Welsh',
              'yo' => 'Yoruba',
              'zu' => 'Zulu',
            ),
          ),
          1 => 
          array (
            'title' => 'List of available language on the landing page',
            'help' => 'Define a list of Language short code and the name. If only one language is listed, the language picker will not show up',
            'key' => 'FRONT_LANGUAGES',
            'value' => 'EN,English,FR,French',
          ),
          2 => 
          array (
            'title' => 'Time zone',
            'key' => 'TIME_ZONE',
            'value' => 'Europe/Berlin',
            'ftype' => 'select',
            'data' => 
            array (
              'Pacific/Midway' => '(UTC-11:00) Midway Island',
              'Pacific/Samoa' => '(UTC-11:00) Samoa',
              'Pacific/Honolulu' => '(UTC-10:00) Hawaii',
              'US/Alaska' => '(UTC-09:00) Alaska',
              'America/Los_Angeles' => '(UTC-08:00) Pacific Time (US &amp; Canada)',
              'America/Tijuana' => '(UTC-08:00) Tijuana',
              'US/Arizona' => '(UTC-07:00) Arizona',
              'America/Chihuahua' => '(UTC-07:00) La Paz',
              'America/Mazatlan' => '(UTC-07:00) Mazatlan',
              'US/Mountain' => '(UTC-07:00) Mountain Time (US &amp; Canada)',
              'America/Managua' => '(UTC-06:00) Central America',
              'US/Central' => '(UTC-06:00) Central Time (US &amp; Canada)',
              'America/Mexico_City' => '(UTC-06:00) Mexico City',
              'America/Monterrey' => '(UTC-06:00) Monterrey',
              'Canada/Saskatchewan' => '(UTC-06:00) Saskatchewan',
              'America/Bogota' => '(UTC-05:00) Quito',
              'US/Eastern' => '(UTC-05:00) Eastern Time (US &amp; Canada)',
              'US/East-Indiana' => '(UTC-05:00) Indiana (East)',
              'America/Lima' => '(UTC-05:00) Lima',
              'Canada/Atlantic' => '(UTC-04:00) Atlantic Time (Canada)',
              'America/Caracas' => '(UTC-04:30) Caracas',
              'America/La_Paz' => '(UTC-04:00) La Paz',
              'America/Santiago' => '(UTC-04:00) Santiago',
              'Canada/Newfoundland' => '(UTC-03:30) Newfoundland',
              'America/Sao_Paulo' => '(UTC-03:00) Brasilia',
              'America/Argentina/Buenos_Aires' => '(UTC-03:00) Georgetown',
              'America/Godthab' => '(UTC-03:00) Greenland',
              'America/Noronha' => '(UTC-02:00) Mid-Atlantic',
              'Atlantic/Azores' => '(UTC-01:00) Azores',
              'Atlantic/Cape_Verde' => '(UTC-01:00) Cape Verde Is.',
              'Africa/Casablanca' => '(UTC+00:00) Casablanca',
              'Europe/London' => '(UTC+00:00) London',
              'Etc/Greenwich' => '(UTC+00:00) Greenwich Mean Time : Dublin',
              'Europe/Lisbon' => '(UTC+00:00) Lisbon',
              'Africa/Monrovia' => '(UTC+00:00) Monrovia',
              'UTC' => '(UTC+00:00) UTC',
              'Europe/Amsterdam' => '(UTC+01:00) Amsterdam',
              'Europe/Belgrade' => '(UTC+01:00) Belgrade',
              'Europe/Berlin' => '(UTC+01:00) Berlin',
              'Europe/Bern' => '(UTC+01:00) Bern',
              'Europe/Bratislava' => '(UTC+01:00) Bratislava',
              'Europe/Brussels' => '(UTC+01:00) Brussels',
              'Europe/Budapest' => '(UTC+01:00) Budapest',
              'Europe/Copenhagen' => '(UTC+01:00) Copenhagen',
              'Europe/Ljubljana' => '(UTC+01:00) Ljubljana',
              'Europe/Madrid' => '(UTC+01:00) Madrid',
              'Europe/Paris' => '(UTC+01:00) Paris',
              'Europe/Prague' => '(UTC+01:00) Prague',
              'Europe/Rome' => '(UTC+01:00) Rome',
              'Europe/Sarajevo' => '(UTC+01:00) Sarajevo',
              'Europe/Skopje' => '(UTC+01:00) Skopje',
              'Europe/Stockholm' => '(UTC+01:00) Stockholm',
              'Europe/Vienna' => '(UTC+01:00) Vienna',
              'Europe/Warsaw' => '(UTC+01:00) Warsaw',
              'Africa/Lagos' => '(UTC+01:00) West Central Africa',
              'Europe/Zagreb' => '(UTC+01:00) Zagreb',
              'Europe/Athens' => '(UTC+02:00) Athens',
              'Europe/Bucharest' => '(UTC+02:00) Bucharest',
              'Africa/Cairo' => '(UTC+02:00) Cairo',
              'Africa/Harare' => '(UTC+02:00) Harare',
              'Europe/Helsinki' => '(UTC+02:00) Kyiv',
              'Europe/Istanbul' => '(UTC+02:00) Istanbul',
              'Asia/Jerusalem' => '(UTC+02:00) Jerusalem',
              'Africa/Johannesburg' => '(UTC+02:00) Pretoria',
              'Europe/Riga' => '(UTC+02:00) Riga',
              'Europe/Sofia' => '(UTC+02:00) Sofia',
              'Europe/Tallinn' => '(UTC+02:00) Tallinn',
              'Europe/Vilnius' => '(UTC+02:00) Vilnius',
              'Asia/Baghdad' => '(UTC+03:00) Baghdad',
              'Asia/Kuwait' => '(UTC+03:00) Kuwait',
              'Europe/Minsk' => '(UTC+03:00) Minsk',
              'Africa/Nairobi' => '(UTC+03:00) Nairobi',
              'Asia/Riyadh' => '(UTC+03:00) Riyadh',
              'Europe/Volgograd' => '(UTC+03:00) Volgograd',
              'Asia/Tehran' => '(UTC+03:30) Tehran',
              'Asia/Muscat' => '(UTC+04:00) Muscat',
              'Asia/Baku' => '(UTC+04:00) Baku',
              'Europe/Moscow' => '(UTC+04:00) St. Petersburg',
              'Asia/Tbilisi' => '(UTC+04:00) Tbilisi',
              'Asia/Yerevan' => '(UTC+04:00) Yerevan',
              'Asia/Kabul' => '(UTC+04:30) Kabul',
              'Asia/Karachi' => '(UTC+05:00) Karachi',
              'Asia/Tashkent' => '(UTC+05:00) Tashkent',
              'Asia/Calcutta' => '(UTC+05:30) Sri Jayawardenepura',
              'Asia/Kolkata' => '(UTC+05:30) Kolkata',
              'Asia/Katmandu' => '(UTC+05:45) Kathmandu',
              'Asia/Almaty' => '(UTC+06:00) Almaty',
              'Asia/Dhaka' => '(UTC+06:00) Dhaka',
              'Asia/Yekaterinburg' => '(UTC+06:00) Ekaterinburg',
              'Asia/Rangoon' => '(UTC+06:30) Rangoon',
              'Asia/Bangkok' => '(UTC+07:00) Hanoi',
              'Asia/Jakarta' => '(UTC+07:00) Jakarta',
              'Asia/Novosibirsk' => '(UTC+07:00) Novosibirsk',
              'Asia/Hong_Kong' => '(UTC+08:00) Hong Kong',
              'Asia/Chongqing' => '(UTC+08:00) Chongqing',
              'Asia/Krasnoyarsk' => '(UTC+08:00) Krasnoyarsk',
              'Asia/Kuala_Lumpur' => '(UTC+08:00) Kuala Lumpur',
              'Australia/Perth' => '(UTC+08:00) Perth',
              'Asia/Singapore' => '(UTC+08:00) Singapore',
              'Asia/Taipei' => '(UTC+08:00) Taipei',
              'Asia/Ulan_Bator' => '(UTC+08:00) Ulaan Bataar',
              'Asia/Urumqi' => '(UTC+08:00) Urumqi',
              'Asia/Irkutsk' => '(UTC+09:00) Irkutsk',
              'Asia/Tokyo' => '(UTC+09:00) Tokyo',
              'Asia/Seoul' => '(UTC+09:00) Seoul',
              'Australia/Adelaide' => '(UTC+09:30) Adelaide',
              'Australia/Darwin' => '(UTC+09:30) Darwin',
              'Australia/Brisbane' => '(UTC+10:00) Brisbane',
              'Australia/Canberra' => '(UTC+10:00) Canberra',
              'Pacific/Guam' => '(UTC+10:00) Guam',
              'Australia/Hobart' => '(UTC+10:00) Hobart',
              'Australia/Melbourne' => '(UTC+10:00) Melbourne',
              'Pacific/Port_Moresby' => '(UTC+10:00) Port Moresby',
              'Australia/Sydney' => '(UTC+10:00) Sydney',
              'Asia/Yakutsk' => '(UTC+10:00) Yakutsk',
              'Asia/Vladivostok' => '(UTC+11:00) Vladivostok',
              'Pacific/Auckland' => '(UTC+12:00) Wellington',
              'Pacific/Fiji' => '(UTC+12:00) Marshall Is.',
              'Pacific/Kwajalein' => '(UTC+12:00) International Date Line West',
              'Asia/Kamchatka' => '(UTC+12:00) Kamchatka',
              'Asia/Magadan' => '(UTC+12:00) Solomon Is.',
              'Pacific/Tongatapu' => '(UTC+13:00) Nuku\'alofa',
            ),
          ),
          3 => 
          array (
            'title' => 'Default currency',
            'key' => 'CASHIER_CURRENCY',
            'value' => 'usd',
            'ftype' => 'select',
            'data' => 
            array (
              'AED' => 'UAE Dirham - د.إ - AED',
              'AFN' => 'Afghani - ؋ - AFN',
              'ALL' => 'Lek - L - ALL',
              'AMD' => 'Armenian Dram - դր. - AMD',
              'ANG' => 'Netherlands Antillean Guilder - ƒ - ANG',
              'AOA' => 'Kwanza - Kz - AOA',
              'ARS' => 'Argentine Peso - $ - ARS',
              'AUD' => 'Australian Dollar - $ - AUD',
              'AWG' => 'Aruban Florin - ƒ - AWG',
              'AZN' => 'Azerbaijanian Manat - ₼ - AZN',
              'BAM' => 'Convertible Mark - КМ - BAM',
              'BBD' => 'Barbados Dollar - $ - BBD',
              'BDT' => 'Taka - ৳ - BDT',
              'BGN' => 'Bulgarian Lev - лв - BGN',
              'BHD' => 'Bahraini Dinar - ب.د - BHD',
              'BIF' => 'Burundi Franc - Fr - BIF',
              'BMD' => 'Bermudian Dollar - $ - BMD',
              'BND' => 'Brunei Dollar - $ - BND',
              'BOB' => 'Boliviano - Bs. - BOB',
              'BOV' => 'Mvdol - Bs. - BOV',
              'BRL' => 'Brazilian Real - R$ - BRL',
              'BSD' => 'Bahamian Dollar - $ - BSD',
              'BTN' => 'Ngultrum - Nu. - BTN',
              'BWP' => 'Pula - P - BWP',
              'BYN' => 'Belarussian Ruble - Br - BYN',
              'BZD' => 'Belize Dollar - $ - BZD',
              'CAD' => 'Canadian Dollar - $ - CAD',
              'CDF' => 'Congolese Franc - Fr - CDF',
              'CHF' => 'Swiss Franc - CHF - CHF',
              'CLF' => 'Unidades de fomento - UF - CLF',
              'CLP' => 'Chilean Peso - $ - CLP',
              'CNY' => 'Yuan Renminbi - ¥ - CNY',
              'COP' => 'Colombian Peso - $ - COP',
              'CRC' => 'Costa Rican Colon - ₡ - CRC',
              'CUC' => 'Peso Convertible - $ - CUC',
              'CUP' => 'Cuban Peso - $ - CUP',
              'CVE' => 'Cape Verde Escudo - $ - CVE',
              'CZK' => 'Czech Koruna - Kč - CZK',
              'DJF' => 'Djibouti Franc - Fdj - DJF',
              'DKK' => 'Danish Krone - kr - DKK',
              'DOP' => 'Dominican Peso - $ - DOP',
              'DZD' => 'Algerian Dinar - د.ج - DZD',
              'EGP' => 'Egyptian Pound - ج.م - EGP',
              'ERN' => 'Nakfa - Nfk - ERN',
              'ETB' => 'Ethiopian Birr - Br - ETB',
              'EUR' => 'Euro - € - EUR',
              'FJD' => 'Fiji Dollar - $ - FJD',
              'FKP' => 'Falkland Islands Pound - £ - FKP',
              'GBP' => 'Pound Sterling - £ - GBP',
              'GEL' => 'Lari - ლ - GEL',
              'GHS' => 'Ghana Cedi - ₵ - GHS',
              'GIP' => 'Gibraltar Pound - £ - GIP',
              'GMD' => 'Dalasi - D - GMD',
              'GNF' => 'Guinea Franc - Fr - GNF',
              'GTQ' => 'Quetzal - Q - GTQ',
              'GYD' => 'Guyana Dollar - $ - GYD',
              'HKD' => 'Hong Kong Dollar - $ - HKD',
              'HNL' => 'Lempira - L - HNL',
              'HRK' => 'Croatian Kuna - kn - HRK',
              'HTG' => 'Gourde - G - HTG',
              'HUF' => 'Forint - Ft - HUF',
              'IDR' => 'Rupiah - Rp - IDR',
              'ILS' => 'New Israeli Sheqel - ₪ - ILS',
              'INR' => 'Indian Rupee - ₹ - INR',
              'IQD' => 'Iraqi Dinar - ع.د - IQD',
              'IRR' => 'Iranian Rial - ﷼ - IRR',
              'ISK' => 'Iceland Krona - kr - ISK',
              'JMD' => 'Jamaican Dollar - $ - JMD',
              'JOD' => 'Jordanian Dinar - د.ا - JOD',
              'JPY' => 'Yen - ¥ - JPY',
              'KES' => 'Kenyan Shilling - KSh - KES',
              'KGS' => 'Som - som - KGS',
              'KHR' => 'Riel - ៛ - KHR',
              'KMF' => 'Comoro Franc - Fr - KMF',
              'KPW' => 'North Korean Won - ₩ - KPW',
              'KRW' => 'Won - ₩ - KRW',
              'KWD' => 'Kuwaiti Dinar - د.ك - KWD',
              'KYD' => 'Cayman Islands Dollar - $ - KYD',
              'KZT' => 'Tenge - 〒 - KZT',
              'LAK' => 'Kip - ₭ - LAK',
              'LBP' => 'Lebanese Pound - ل.ل - LBP',
              'LKR' => 'Sri Lanka Rupee - ₨ - LKR',
              'LRD' => 'Liberian Dollar - $ - LRD',
              'LSL' => 'Loti - L - LSL',
              'LTL' => 'Lithuanian Litas - Lt - LTL',
              'LVL' => 'Latvian Lats - Ls - LVL',
              'LYD' => 'Libyan Dinar - ل.د - LYD',
              'MAD' => 'Moroccan Dirham - د.م. - MAD',
              'MDL' => 'Moldovan Leu - L - MDL',
              'MGA' => 'Malagasy Ariary - Ar - MGA',
              'MKD' => 'Denar - ден - MKD',
              'MMK' => 'Kyat - K - MMK',
              'MNT' => 'Tugrik - ₮ - MNT',
              'MOP' => 'Pataca - P - MOP',
              'MRO' => 'Ouguiya - UM - MRO',
              'MUR' => 'Mauritius Rupee - ₨ - MUR',
              'MVR' => 'Rufiyaa - MVR - MVR',
              'MWK' => 'Kwacha - MK - MWK',
              'MXN' => 'Mexican Peso - $ - MXN',
              'MYR' => 'Malaysian Ringgit - RM - MYR',
              'MZN' => 'Mozambique Metical - MTn - MZN',
              'NAD' => 'Namibia Dollar - $ - NAD',
              'NGN' => 'Naira - ₦ - NGN',
              'NIO' => 'Cordoba Oro - C$ - NIO',
              'NOK' => 'Norwegian Krone - kr - NOK',
              'NPR' => 'Nepalese Rupee - ₨ - NPR',
              'NZD' => 'New Zealand Dollar - $ - NZD',
              'OMR' => 'Rial Omani - ر.ع. - OMR',
              'PAB' => 'Balboa - B/. - PAB',
              'PEN' => 'Sol - S/ - PEN',
              'PGK' => 'Kina - K - PGK',
              'PHP' => 'Philippine Peso - ₱ - PHP',
              'PKR' => 'Pakistan Rupee - ₨ - PKR',
              'PLN' => 'Zloty - zł - PLN',
              'PYG' => 'Guarani - ₲ - PYG',
              'QAR' => 'Qatari Rial - ر.ق - QAR',
              'RON' => 'New Romanian Leu - Lei - RON',
              'RSD' => 'Serbian Dinar - РСД - RSD',
              'RUB' => 'Russian Ruble - ₽ - RUB',
              'RWF' => 'Rwanda Franc - FRw - RWF',
              'SAR' => 'Saudi Riyal - ر.س - SAR',
              'SBD' => 'Solomon Islands Dollar - $ - SBD',
              'SCR' => 'Seychelles Rupee - ₨ - SCR',
              'SDG' => 'Sudanese Pound - £ - SDG',
              'SEK' => 'Swedish Krona - kr - SEK',
              'SGD' => 'Singapore Dollar - $ - SGD',
              'SHP' => 'Saint Helena Pound - £ - SHP',
              'SLL' => 'Leone - Le - SLL',
              'SOS' => 'Somali Shilling - Sh - SOS',
              'SRD' => 'Surinam Dollar - $ - SRD',
              'SSP' => 'South Sudanese Pound - £ - SSP',
              'STD' => 'Dobra - Db - STD',
              'SVC' => 'El Salvador Colon - ₡ - SVC',
              'SYP' => 'Syrian Pound - £S - SYP',
              'SZL' => 'Lilangeni - E - SZL',
              'THB' => 'Baht - ฿ - THB',
              'TJS' => 'Somoni - ЅМ - TJS',
              'TMT' => 'Turkmenistan New Manat - T - TMT',
              'TND' => 'Tunisian Dinar - د.ت - TND',
              'TOP' => 'Pa’anga - T$ - TOP',
              'TRY' => 'Turkish Lira - ₺ - TRY',
              'TTD' => 'Trinidad and Tobago Dollar - $ - TTD',
              'TWD' => 'New Taiwan Dollar - $ - TWD',
              'TZS' => 'Tanzanian Shilling - Sh - TZS',
              'UAH' => 'Hryvnia - ₴ - UAH',
              'UGX' => 'Uganda Shilling - USh - UGX',
              'USD' => 'US Dollar - $ - USD',
              'UYU' => 'Peso Uruguayo - $ - UYU',
              'UZS' => 'Uzbekistan Sum - лв - UZS',
              'VEF' => 'Bolivar - Bs F - VEF',
              'VND' => 'Dong - ₫ - VND',
              'VUV' => 'Vatu - Vt - VUV',
              'WST' => 'Tala - T - WST',
              'XAF' => 'CFA Franc BEAC - Fr - XAF',
              'XAG' => 'Silver - oz t - XAG',
              'XAU' => 'Gold - oz t - XAU',
              'XCD' => 'East Caribbean Dollar - $ - XCD',
              'XDR' => 'SDR (Special Drawing Right) - SDR - XDR',
              'XOF' => 'CFA Franc BCEAO - Fr - XOF',
              'XPF' => 'CFP Franc - Fr - XPF',
              'YER' => 'Yemeni Rial - ﷼ - YER',
              'ZAR' => 'Rand - R - ZAR',
              'ZMW' => 'Zambian Kwacha - ZK - ZMW',
              'ZWL' => 'Zimbabwe Dollar - $ - ZWL',
            ),
          ),
          4 => 
          array (
            'title' => 'Money conversion',
            'help' => 'Some currencies need this field to be unselected. By default it should be selected',
            'key' => 'DO_CONVERTION',
            'value' => 'true',
            'ftype' => 'bool',
          ),
          5 => 
          array (
            'title' => 'Time format',
            'key' => 'TIME_FORMAT',
            'value' => 'AM/PM',
            'ftype' => 'select',
            'data' => 
            array (
              'AM/PM' => 'AM/PM',
              '24hours ' => '24 Hours',
            ),
          ),
          6 => 
          array (
            'title' => 'Date and time display',
            'key' => 'DATETIME_DISPLAY_FORMAT',
            'value' => 'd M Y h:i A',
          ),
          7 => 
          array (
            'title' => 'Working time display format',
            'help' => 'For 24h use \'E HH:mm\' and for AM/PM use \'E h:mm a\'',
            'key' => 'DATETIME_WORKING_HOURS_DISPLAY_FORMAT_NEW',
            'value' => 'E HH:mm',
          ),
          8 => 
          array (
            'title' => 'Default phone country code ',
            'help' => '2 characters -  ISO 3166-1 alpha-2 format',
            'key' => 'DEFAULT_COUNTRY',
            'value' => 'US',
          ),
        ),
      ),
      3 => 
      array (
        'name' => 'Apps & Plugins',
        'slug' => 'plugins',
        'icon' => 'ni ni-spaceship',
        'fields' => 
        array (
          0 => 
          array (
            'separator' => 'Tools',
            'title' => 'Recaptcha secret',
            'help' => 'Make empty if you can\'t make submit on register screen',
            'key' => 'RECAPTCHA_SECRET_KEY',
            'value' => '',
          ),
          1 => 
          array (
            'separator' => 'Pusher live notifications',
            'title' => 'Pusher app id',
            'help' => 'Pusher is used for live notifications',
            'key' => 'PUSHER_APP_ID',
            'value' => '',
          ),
          2 => 
          array (
            'title' => 'Pusher app key',
            'key' => 'PUSHER_APP_KEY',
            'value' => '',
          ),
          3 => 
          array (
            'title' => 'Pusher app secret',
            'key' => 'PUSHER_APP_SECRET',
            'value' => '',
          ),
          4 => 
          array (
            'title' => 'Pusher app cluster',
            'key' => 'PUSHER_APP_CLUSTER',
            'value' => 'eu',
          ),
          5 => 
          array (
            'title' => 'Broadcast Driver',
            'key' => 'BROADCAST_DRIVER',
            'value' => 'log',
            'ftype' => 'select',
            'data' => 
            array (
              'pusher' => 'Pusher',
              'log' => 'Log',
            ),
          ),
          6 => 
          array (
            'separator' => 'Share this',
            'title' => 'Share this property id',
            'help' => 'You can find this number in Share this import link',
            'key' => 'SHARE_THIS_PROPERTY',
            'value' => '',
          ),
        ),
      ),
      4 => 
      array (
        'name' => 'SMTP',
        'slug' => 'smtp',
        'icon' => 'ni ni-email-83',
        'fields' => 
        array (
          0 => 
          array (
            'title' => 'Mail driver',
            'key' => 'MAIL_MAILER',
            'value' => 'smtp',
            'ftype' => 'select',
            'data' => 
            array (
              'smtp' => 'SMTP',
              'sendmail' => 'PHP Sendmail - best of port 465',
            ),
          ),
          1 => 
          array (
            'title' => 'Host',
            'key' => 'MAIL_HOST',
            'value' => 'smtp.mailtrap.io',
            'hint' => 'Your SMTP send server',
          ),
          2 => 
          array (
            'title' => 'Port',
            'key' => 'MAIL_PORT',
            'value' => '2525',
            'help' => 'Common ports are 26, 465, 587',
          ),
          3 => 
          array (
            'title' => 'Encryption',
            'key' => 'MAIL_ENCRYPTION',
            'value' => '',
            'ftype' => 'select',
            'data' => 
            array (
              'null' => 'Null - best for port 26',
              '' => 'None - best for port 587',
              'ssl' => 'SSL - best for port 465',
              'tls' => 'TLS',
              'starttls' => 'STARTTLS',
            ),
          ),
          4 => 
          array (
            'title' => 'Username',
            'key' => 'MAIL_USERNAME',
            'value' => '802fc656dd8029',
          ),
          5 => 
          array (
            'title' => 'Password',
            'key' => 'MAIL_PASSWORD',
            'value' => 'bbcf39d313eac6',
          ),
          6 => 
          array (
            'title' => 'From address',
            'key' => 'MAIL_FROM_ADDRESS',
            'value' => '<EMAIL>',
          ),
          7 => 
          array (
            'title' => 'From Name',
            'key' => 'MAIL_FROM_NAME',
            'value' => 'Your Site',
          ),
          8 => 
          array (
            'title' => '',
            'key' => 'DB_CONNECTION',
            'value' => 'mysql',
            'data' => 
            array (
              'mysql' => 'MySql',
            ),
            'type' => 'hidden',
          ),
          9 => 
          array (
            'title' => '',
            'key' => 'DB_HOST',
            'value' => '127.0.0.1',
            'hint' => 'Your SMTP send server',
            'type' => 'hidden',
          ),
          10 => 
          array (
            'title' => '',
            'key' => 'DB_PORT',
            'value' => '3306',
            'type' => 'hidden',
          ),
          11 => 
          array (
            'title' => '',
            'key' => 'DB_DATABASE',
            'value' => 'laravel',
            'type' => 'hidden',
          ),
          12 => 
          array (
            'title' => '',
            'key' => 'DB_USERNAME',
            'value' => 'laravel',
            'type' => 'hidden',
          ),
          13 => 
          array (
            'title' => '',
            'key' => 'DB_PASSWORD',
            'value' => 'laravel',
            'type' => 'hidden',
          ),
          14 => 
          array (
            'title' => '',
            'key' => 'CACHE_DRIVER',
            'value' => 'file',
            'type' => 'hidden',
          ),
          15 => 
          array (
            'title' => '',
            'key' => 'SESSION_DRIVER',
            'value' => 'file',
            'type' => 'hidden',
          ),
          16 => 
          array (
            'title' => '',
            'key' => 'REDIS_HOST',
            'value' => '127.0.0.1',
            'type' => 'hidden',
          ),
          17 => 
          array (
            'title' => '',
            'key' => 'REDIS_PASSWORD',
            'value' => 'null',
            'type' => 'hidden',
          ),
          18 => 
          array (
            'title' => '',
            'key' => 'REDIS_PORT',
            'value' => '6379',
            'type' => 'hidden',
          ),
        ),
      ),
    ),
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'zaptra-demo',
        'prefix' => '',
        'foreign_key_constraints' => true,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'zaptra-demo',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'zaptra-demo',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'zaptra-demo',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'zaptra_database_',
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => '',
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => '',
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\zaptra\\storage\\app',
        'throw' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\zaptra\\storage\\app/public',
        'url' => 'http://localhost/zaptra/public/storage',
        'visibility' => 'public',
        'throw' => false,
      ),
      'public_uploads' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\zaptra\\public/uploads',
        'url' => 'http://localhost/zaptra/public/uploads',
        'visibility' => 'public',
        'throw' => false,
      ),
      'public_media_upload' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\zaptra\\public/uploads/media',
        'url' => 'http://localhost/zaptra/public/uploads/media',
        'visibility' => 'public',
        'throw' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => true,
      ),
    ),
    'links' => 
    array (
      'C:\\xampp\\htdocs\\zaptra\\public\\storage' => 'C:\\xampp\\htdocs\\zaptra\\storage\\app/public',
    ),
  ),
  'fortify-options' => 
  array (
    'two-factor-authentication' => 
    array (
      'confirm' => true,
      'confirmPassword' => true,
    ),
  ),
  'fortify' => 
  array (
    'guard' => 'web',
    'middleware' => 
    array (
      0 => 'web',
    ),
    'auth_middleware' => 'auth',
    'passwords' => 'users',
    'username' => 'email',
    'email' => 'email',
    'views' => true,
    'home' => '/dashboard',
    'prefix' => '',
    'domain' => NULL,
    'lowercase_usernames' => false,
    'limiters' => 
    array (
      'login' => 'login',
      'two-factor' => 'two-factor',
    ),
    'paths' => 
    array (
      'login' => NULL,
      'logout' => NULL,
      'password' => 
      array (
        'request' => NULL,
        'reset' => NULL,
        'email' => NULL,
        'update' => NULL,
        'confirm' => NULL,
        'confirmation' => NULL,
      ),
      'register' => NULL,
      'verification' => 
      array (
        'notice' => NULL,
        'verify' => NULL,
        'send' => NULL,
      ),
      'user-profile-information' => 
      array (
        'update' => NULL,
      ),
      'user-password' => 
      array (
        'update' => NULL,
      ),
      'two-factor' => 
      array (
        'login' => NULL,
        'enable' => NULL,
        'confirm' => NULL,
        'disable' => NULL,
        'qr-code' => NULL,
        'secret-key' => NULL,
        'recovery-codes' => NULL,
      ),
    ),
    'redirects' => 
    array (
      'login' => NULL,
      'logout' => NULL,
      'password-confirmation' => NULL,
      'register' => NULL,
      'email-verification' => NULL,
      'password-reset' => NULL,
    ),
    'features' => 
    array (
      0 => 'registration',
      1 => 'reset-passwords',
      2 => 'update-profile-information',
      3 => 'update-passwords',
      4 => 'two-factor-authentication',
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 10,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
    ),
  ),
  'installer' => 
  array (
    'icon' => '/uploads/default/logo.png',
    'background' => '/uploads/default/bg.jpg',
    'support_url' => 'https://help.mobidonia.com/',
    'server' => 
    array (
      'php' => 
      array (
        'name' => 'PHP Version',
        'version' => '>= 8.1.0',
        'check' => 
        array (
          'type' => 'php',
          'value' => 80100,
        ),
      ),
      'pdo' => 
      array (
        'name' => 'PDO',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'pdo_mysql',
        ),
      ),
      'mbstring' => 
      array (
        'name' => 'Mbstring extension',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'mbstring',
        ),
      ),
      'fileinfo' => 
      array (
        'name' => 'Fileinfo extension',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'fileinfo',
        ),
      ),
      'openssl' => 
      array (
        'name' => 'OpenSSL extension',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'openssl',
        ),
      ),
      'tokenizer' => 
      array (
        'name' => 'Tokenizer extension',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'tokenizer',
        ),
      ),
      'json' => 
      array (
        'name' => 'Json extension',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'json',
        ),
      ),
      'curl' => 
      array (
        'name' => 'Curl extension',
        'check' => 
        array (
          'type' => 'extension',
          'value' => 'curl',
        ),
      ),
    ),
    'folders' => 
    array (
      'storage.framework' => 
      array (
        'name' => '/storage/framework',
        'check' => 
        array (
          'type' => 'directory',
          'value' => '../storage/framework',
        ),
      ),
      'storage.logs' => 
      array (
        'name' => '/storage/logs',
        'check' => 
        array (
          'type' => 'directory',
          'value' => '../storage/logs',
        ),
      ),
      'storage.cache' => 
      array (
        'name' => '/bootstrap/cache',
        'check' => 
        array (
          'type' => 'directory',
          'value' => '../bootstrap/cache',
        ),
      ),
      'storage.uploads' => 
      array (
        'name' => '/public/uploads',
        'check' => 
        array (
          'type' => 'directory',
          'value' => '../public/uploads',
        ),
      ),
    ),
    'database' => 
    array (
      'seeders' => true,
    ),
    'commands' => 
    array (
      0 => 'app:migrrate-modules',
    ),
    'admin_area' => 
    array (
      'user' => 
      array (
        'email' => '<EMAIL>',
        'password' => 'secret',
      ),
    ),
    'login' => '/login',
  ),
  'jetstream' => 
  array (
    'stack' => 'livewire',
    'middleware' => 
    array (
      0 => 'web',
    ),
    'features' => 
    array (
      0 => 'terms',
      1 => 'api',
      2 => 'account-deletion',
    ),
    'profile_photo_disk' => 'public',
    'auth_session' => 'Laravel\\Jetstream\\Http\\Middleware\\AuthenticateSession',
    'guard' => 'sanctum',
  ),
  'languages' => 
  array (
    'af' => 'Afrikaans',
    'ak' => 'Akan',
    'sq' => 'Albanian',
    'am' => 'Amharic',
    'ar' => 'Arabic',
    'hy' => 'Armenian',
    'as' => 'Assamese',
    'az' => 'Azerbaijani',
    'bm' => 'Bambara',
    'eu' => 'Basque',
    'be' => 'Belarusian',
    'bn' => 'Bengali',
    'bs' => 'Bosnian',
    'bg' => 'Bulgarian',
    'my' => 'Burmese',
    'ca' => 'Catalan',
    'zh' => 'Chinese',
    'kw' => 'Cornish',
    'hr' => 'Croatian',
    'cs' => 'Czech',
    'da' => 'Danish',
    'nl' => 'Dutch',
    'en' => 'English',
    'eo' => 'Esperanto',
    'et' => 'Estonian',
    'ee' => 'Ewe',
    'fo' => 'Faroese',
    'fi' => 'Finnish',
    'fr' => 'French',
    'ff' => 'Fulah',
    'gl' => 'Galician',
    'lg' => 'Ganda',
    'ka' => 'Georgian',
    'de' => 'German',
    'el' => 'Greek',
    'gu' => 'Gujarati',
    'ha' => 'Hausa',
    'he' => 'Hebrew',
    'hi' => 'Hindi',
    'hu' => 'Hungarian',
    'is' => 'Icelandic',
    'ig' => 'Igbo',
    'id' => 'Indonesian',
    'ga' => 'Irish',
    'it' => 'Italian',
    'ja' => 'Japanese',
    'kl' => 'Kalaallisut',
    'kn' => 'Kannada',
    'kk' => 'Kazakh',
    'km' => 'Khmer',
    'ki' => 'Kikuyu',
    'rw' => 'Kinyarwanda',
    'ko' => 'Korean',
    'lv' => 'Latvian',
    'lt' => 'Lithuanian',
    'mk' => 'Macedonian',
    'mg' => 'Malagasy',
    'ms' => 'Malay',
    'ml' => 'Malayalam',
    'mt' => 'Maltese',
    'gv' => 'Manx',
    'mr' => 'Marathi',
    'ne' => 'Nepali',
    'nd' => 'North Ndebele',
    'nb' => 'Norwegian Bokmål',
    'nn' => 'Norwegian Nynorsk',
    'or' => 'Oriya',
    'om' => 'Oromo',
    'ps' => 'Pashto',
    'fa' => 'Persian',
    'pl' => 'Polish',
    'pt' => 'Portuguese',
    'pa' => 'Punjabi',
    'ro' => 'Romanian',
    'rm' => 'Romansh',
    'ru' => 'Russian',
    'sg' => 'Sango',
    'sr' => 'Serbian',
    'sn' => 'Shona',
    'ii' => 'Sichuan Yi',
    'si' => 'Sinhala',
    'sk' => 'Slovak',
    'sl' => 'Slovenian',
    'so' => 'Somali',
    'es' => 'Spanish',
    'sw' => 'Swahili',
    'sv' => 'Swedish',
    'ta' => 'Tamil',
    'te' => 'Telugu',
    'th' => 'Thai',
    'bo' => 'Tibetan',
    'ti' => 'Tigrinya',
    'to' => 'Tonga',
    'tr' => 'Turkish',
    'uk' => 'Ukrainian',
    'ur' => 'Urdu',
    'uz' => 'Uzbek',
    'vi' => 'Vietnamese',
    'cy' => 'Welsh',
    'yo' => 'Yoruba',
    'zu' => 'Zulu',
  ),
  'livewire' => 
  array (
    'class_namespace' => 'App\\Livewire',
    'view_path' => 'C:\\xampp\\htdocs\\zaptra\\resources\\views/livewire',
    'layout' => 'layouts.app',
    'lazy_placeholder' => NULL,
    'temporary_file_upload' => 
    array (
      'disk' => NULL,
      'rules' => NULL,
      'directory' => NULL,
      'middleware' => NULL,
      'preview_mimes' => 
      array (
        0 => 'png',
        1 => 'gif',
        2 => 'bmp',
        3 => 'svg',
        4 => 'wav',
        5 => 'mp4',
        6 => 'mov',
        7 => 'avi',
        8 => 'wmv',
        9 => 'mp3',
        10 => 'm4a',
        11 => 'jpg',
        12 => 'jpeg',
        13 => 'mpga',
        14 => 'webp',
        15 => 'wma',
      ),
      'max_upload_time' => 5,
    ),
    'render_on_redirect' => false,
    'legacy_model_binding' => false,
    'inject_assets' => true,
    'navigate' => 
    array (
      'show_progress_bar' => true,
      'progress_bar_color' => '#2299dd',
    ),
    'inject_morph_markers' => true,
    'pagination_theme' => 'tailwind',
  ),
  'log-viewer' => 
  array (
    'enabled' => false,
    'api_only' => false,
    'require_auth_in_production' => true,
    'route_domain' => NULL,
    'route_path' => 'log-viewer',
    'back_to_system_url' => 'http://localhost/zaptra/public',
    'back_to_system_label' => NULL,
    'timezone' => NULL,
    'datetime_format' => 'Y-m-d H:i:s',
    'middleware' => 
    array (
      0 => 'web',
      1 => 'Opcodes\\LogViewer\\Http\\Middleware\\AuthorizeLogViewer',
    ),
    'api_middleware' => 
    array (
      0 => 'Opcodes\\LogViewer\\Http\\Middleware\\EnsureFrontendRequestsAreStateful',
      1 => 'Opcodes\\LogViewer\\Http\\Middleware\\AuthorizeLogViewer',
    ),
    'api_stateful_domains' => NULL,
    'hosts' => 
    array (
      'local' => 
      array (
        'name' => 'Production',
      ),
    ),
    'include_files' => 
    array (
      0 => '*.log',
      1 => '**/*.log',
      2 => '/var/log/httpd/*',
      3 => '/var/log/nginx/*',
      4 => '/opt/homebrew/var/log/nginx/*',
      5 => '/opt/homebrew/var/log/httpd/*',
      6 => '/opt/homebrew/var/log/php-fpm.log',
      7 => '/opt/homebrew/var/log/postgres*log',
      8 => '/opt/homebrew/var/log/redis*log',
      9 => '/opt/homebrew/var/log/supervisor*log',
    ),
    'exclude_files' => 
    array (
    ),
    'hide_unknown_files' => true,
    'shorter_stack_trace_excludes' => 
    array (
      0 => '/vendor/symfony/',
      1 => '/vendor/laravel/framework/',
      2 => '/vendor/barryvdh/laravel-debugbar/',
    ),
    'cache_driver' => NULL,
    'cache_key_prefix' => 'lv',
    'lazy_scan_chunk_size_in_mb' => 50,
    'strip_extracted_context' => true,
    'per_page_options' => 
    array (
      0 => 10,
      1 => 25,
      2 => 50,
      3 => 100,
      4 => 250,
      5 => 500,
    ),
    'defaults' => 
    array (
      'use_local_storage' => true,
      'folder_sorting_method' => 'ModifiedTime',
      'folder_sorting_order' => 'desc',
      'log_sorting_order' => 'desc',
      'per_page' => 25,
      'theme' => 'System',
      'shorter_stack_traces' => false,
    ),
    'exclude_ip_from_identifiers' => false,
    'root_folder_prefix' => 'root',
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\zaptra\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\zaptra\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\xampp\\htdocs\\zaptra\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'url' => NULL,
        'host' => 'sv90.ifastnet.com',
        'port' => '465',
        'encryption' => 'ssl',
        'username' => '<EMAIL>',
        'password' => 'Prasad@143',
        'timeout' => NULL,
        'local_domain' => NULL,
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Zaptra',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\xampp\\htdocs\\zaptra\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'module' => 
  array (
    'namespace' => 'Modules',
    'stubs' => 
    array (
      'enabled' => false,
      'path' => 'C:\\xampp\\htdocs\\zaptra/vendor/akaunting/module/src/Commands/stubs',
      'files' => 
      array (
        'routes/web' => 'Routes/web.php',
        'routes/api' => 'Routes/api.php',
        'lang/general' => 'Resources/lang/en/general.php',
        'views/index' => 'Resources/views/index.blade.php',
        'views/master' => 'Resources/views/layouts/master.blade.php',
        'scaffold/config' => 'Config/config.php',
        'composer' => 'composer.json',
        'assets/js/app' => 'Resources/assets/js/app.js',
        'assets/sass/app' => 'Resources/assets/sass/app.scss',
        'webpack' => 'webpack.mix.js',
        'package' => 'package.json',
      ),
      'replacements' => 
      array (
        'routes/web' => 
        array (
          0 => 'ALIAS',
          1 => 'STUDLY_NAME',
        ),
        'routes/api' => 
        array (
          0 => 'ALIAS',
          1 => 'STUDLY_NAME',
        ),
        'webpack' => 
        array (
          0 => 'ALIAS',
        ),
        'json' => 
        array (
          0 => 'ALIAS',
          1 => 'STUDLY_NAME',
          2 => 'MODULE_NAMESPACE',
        ),
        'lang/general' => 
        array (
          0 => 'ALIAS',
          1 => 'STUDLY_NAME',
        ),
        'views/index' => 
        array (
          0 => 'ALIAS',
          1 => 'STUDLY_NAME',
        ),
        'views/master' => 
        array (
          0 => 'ALIAS',
          1 => 'STUDLY_NAME',
        ),
        'scaffold/config' => 
        array (
          0 => 'STUDLY_NAME',
        ),
        'composer' => 
        array (
          0 => 'LOWER_NAME',
          1 => 'STUDLY_NAME',
          2 => 'VENDOR',
          3 => 'AUTHOR_NAME',
          4 => 'AUTHOR_EMAIL',
          5 => 'MODULE_NAMESPACE',
        ),
      ),
      'gitkeep' => true,
    ),
    'paths' => 
    array (
      'modules' => 'C:\\xampp\\htdocs\\zaptra\\modules',
      'assets' => 'C:\\xampp\\htdocs\\zaptra\\public\\modules',
      'migration' => 'C:\\xampp\\htdocs\\zaptra\\database/migrations',
      'generator' => 
      array (
        'config' => 
        array (
          'path' => 'Config',
          'generate' => true,
        ),
        'command' => 
        array (
          'path' => 'Console',
          'generate' => true,
        ),
        'migration' => 
        array (
          'path' => 'Database/Migrations',
          'generate' => true,
        ),
        'seeder' => 
        array (
          'path' => 'Database/Seeds',
          'generate' => true,
        ),
        'factory' => 
        array (
          'path' => 'Database/Factories',
          'generate' => true,
        ),
        'model' => 
        array (
          'path' => 'Models',
          'generate' => true,
        ),
        'controller' => 
        array (
          'path' => 'Http/Controllers',
          'generate' => true,
        ),
        'middleware' => 
        array (
          'path' => 'Http/Middleware',
          'generate' => true,
        ),
        'request' => 
        array (
          'path' => 'Http/Requests',
          'generate' => true,
        ),
        'resource' => 
        array (
          'path' => 'Http/Resources',
          'generate' => false,
        ),
        'provider' => 
        array (
          'path' => 'Providers',
          'generate' => true,
        ),
        'asset' => 
        array (
          'path' => 'Resources/assets',
          'generate' => true,
        ),
        'lang' => 
        array (
          'path' => 'Resources/lang/en',
          'generate' => true,
        ),
        'view' => 
        array (
          'path' => 'Resources/views',
          'generate' => true,
        ),
        'test' => 
        array (
          'path' => 'Tests',
          'generate' => false,
        ),
        'repository' => 
        array (
          'path' => 'Repositories',
          'generate' => false,
        ),
        'event' => 
        array (
          'path' => 'Events',
          'generate' => false,
        ),
        'listener' => 
        array (
          'path' => 'Listeners',
          'generate' => true,
        ),
        'policy' => 
        array (
          'path' => 'Policies',
          'generate' => false,
        ),
        'rules' => 
        array (
          'path' => 'Rules',
          'generate' => false,
        ),
        'job' => 
        array (
          'path' => 'Jobs',
          'generate' => false,
        ),
        'email' => 
        array (
          'path' => 'Emails',
          'generate' => false,
        ),
        'notification' => 
        array (
          'path' => 'Notifications',
          'generate' => false,
        ),
        'route' => 
        array (
          'path' => 'Routes',
          'generate' => true,
        ),
        'component' => 
        array (
          'path' => 'View/Components',
          'generate' => false,
        ),
        'cast' => 
        array (
          'path' => 'Casts',
          'generate' => false,
        ),
        'observer' => 
        array (
          'path' => 'Observers',
          'generate' => false,
        ),
        'exception' => 
        array (
          'path' => 'Exceptions',
          'generate' => false,
        ),
      ),
    ),
    'scan' => 
    array (
      'enabled' => false,
      'paths' => 
      array (
        0 => 'C:\\xampp\\htdocs\\zaptra\\vendor/*/*',
      ),
    ),
    'composer' => 
    array (
      'vendor' => 'akaunting',
      'author' => 
      array (
        'name' => 'Akaunting',
        'email' => '<EMAIL>',
      ),
    ),
    'cache' => 
    array (
      'enabled' => false,
      'key' => 'module',
      'lifetime' => 60,
    ),
    'register' => 
    array (
      'translations' => true,
      'files' => 'register',
    ),
  ),
  'money' => 
  array (
    'AED' => 
    array (
      'name' => 'UAE Dirham',
      'code' => 784,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'د.إ',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'AFN' => 
    array (
      'name' => 'Afghani',
      'code' => 971,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '؋',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ALL' => 
    array (
      'name' => 'Lek',
      'code' => 8,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'L',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'AMD' => 
    array (
      'name' => 'Armenian Dram',
      'code' => 51,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'դր.',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ANG' => 
    array (
      'name' => 'Netherlands Antillean Guilder',
      'code' => 532,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ƒ',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'AOA' => 
    array (
      'name' => 'Kwanza',
      'code' => 973,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Kz',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ARS' => 
    array (
      'name' => 'Argentine Peso',
      'code' => 32,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'AUD' => 
    array (
      'name' => 'Australian Dollar',
      'code' => 36,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ' ',
    ),
    'AWG' => 
    array (
      'name' => 'Aruban Florin',
      'code' => 533,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ƒ',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'AZN' => 
    array (
      'name' => 'Azerbaijanian Manat',
      'code' => 944,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₼',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BAM' => 
    array (
      'name' => 'Convertible Mark',
      'code' => 977,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'КМ',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BBD' => 
    array (
      'name' => 'Barbados Dollar',
      'code' => 52,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BDT' => 
    array (
      'name' => 'Taka',
      'code' => 50,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '৳',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BGN' => 
    array (
      'name' => 'Bulgarian Lev',
      'code' => 975,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'лв',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => ' ',
    ),
    'BHD' => 
    array (
      'name' => 'Bahraini Dinar',
      'code' => 48,
      'precision' => 3,
      'subunit' => 1000,
      'symbol' => 'ب.د',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BIF' => 
    array (
      'name' => 'Burundi Franc',
      'code' => 108,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BMD' => 
    array (
      'name' => 'Bermudian Dollar',
      'code' => 60,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BND' => 
    array (
      'name' => 'Brunei Dollar',
      'code' => 96,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BOB' => 
    array (
      'name' => 'Boliviano',
      'code' => 68,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Bs.',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BOV' => 
    array (
      'name' => 'Mvdol',
      'code' => 984,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Bs.',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BRL' => 
    array (
      'name' => 'Brazilian Real',
      'code' => 986,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'R$',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'BSD' => 
    array (
      'name' => 'Bahamian Dollar',
      'code' => 44,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BTN' => 
    array (
      'name' => 'Ngultrum',
      'code' => 64,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Nu.',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BWP' => 
    array (
      'name' => 'Pula',
      'code' => 72,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'P',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'BYN' => 
    array (
      'name' => 'Belarussian Ruble',
      'code' => 974,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Br',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => ' ',
    ),
    'BZD' => 
    array (
      'name' => 'Belize Dollar',
      'code' => 84,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CAD' => 
    array (
      'name' => 'Canadian Dollar',
      'code' => 124,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CDF' => 
    array (
      'name' => 'Congolese Franc',
      'code' => 976,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CHF' => 
    array (
      'name' => 'Swiss Franc',
      'code' => 756,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'CHF',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CLF' => 
    array (
      'name' => 'Unidades de fomento',
      'code' => 990,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'UF',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'CLP' => 
    array (
      'name' => 'Chilean Peso',
      'code' => 152,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'CNY' => 
    array (
      'name' => 'Yuan Renminbi',
      'code' => 156,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '¥',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'COP' => 
    array (
      'name' => 'Colombian Peso',
      'code' => 170,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'CRC' => 
    array (
      'name' => 'Costa Rican Colon',
      'code' => 188,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₡',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'CUC' => 
    array (
      'name' => 'Peso Convertible',
      'code' => 931,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CUP' => 
    array (
      'name' => 'Cuban Peso',
      'code' => 192,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CVE' => 
    array (
      'name' => 'Cape Verde Escudo',
      'code' => 132,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'CZK' => 
    array (
      'name' => 'Czech Koruna',
      'code' => 203,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Kč',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'DJF' => 
    array (
      'name' => 'Djibouti Franc',
      'code' => 262,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fdj',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'DKK' => 
    array (
      'name' => 'Danish Krone',
      'code' => 208,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'kr',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'DOP' => 
    array (
      'name' => 'Dominican Peso',
      'code' => 214,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'DZD' => 
    array (
      'name' => 'Algerian Dinar',
      'code' => 12,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'د.ج',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'EGP' => 
    array (
      'name' => 'Egyptian Pound',
      'code' => 818,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ج.م',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ERN' => 
    array (
      'name' => 'Nakfa',
      'code' => 232,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Nfk',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ETB' => 
    array (
      'name' => 'Ethiopian Birr',
      'code' => 230,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Br',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'EUR' => 
    array (
      'name' => 'Euro',
      'code' => 978,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '€',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'FJD' => 
    array (
      'name' => 'Fiji Dollar',
      'code' => 242,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'FKP' => 
    array (
      'name' => 'Falkland Islands Pound',
      'code' => 238,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GBP' => 
    array (
      'name' => 'Pound Sterling',
      'code' => 826,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GEL' => 
    array (
      'name' => 'Lari',
      'code' => 981,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ლ',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GHS' => 
    array (
      'name' => 'Ghana Cedi',
      'code' => 936,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₵',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GIP' => 
    array (
      'name' => 'Gibraltar Pound',
      'code' => 292,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GMD' => 
    array (
      'name' => 'Dalasi',
      'code' => 270,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'D',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GNF' => 
    array (
      'name' => 'Guinea Franc',
      'code' => 324,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GTQ' => 
    array (
      'name' => 'Quetzal',
      'code' => 320,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Q',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'GYD' => 
    array (
      'name' => 'Guyana Dollar',
      'code' => 328,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'HKD' => 
    array (
      'name' => 'Hong Kong Dollar',
      'code' => 344,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'HNL' => 
    array (
      'name' => 'Lempira',
      'code' => 340,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'L',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'HRK' => 
    array (
      'name' => 'Croatian Kuna',
      'code' => 191,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'kn',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'HTG' => 
    array (
      'name' => 'Gourde',
      'code' => 332,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'G',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'HUF' => 
    array (
      'name' => 'Forint',
      'code' => 348,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Ft',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'IDR' => 
    array (
      'name' => 'Rupiah',
      'code' => 360,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Rp',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'ILS' => 
    array (
      'name' => 'New Israeli Sheqel',
      'code' => 376,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₪',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'INR' => 
    array (
      'name' => 'Indian Rupee',
      'code' => 356,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₹',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'IQD' => 
    array (
      'name' => 'Iraqi Dinar',
      'code' => 368,
      'precision' => 3,
      'subunit' => 1000,
      'symbol' => 'ع.د',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'IRR' => 
    array (
      'name' => 'Iranian Rial',
      'code' => 364,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '﷼',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ISK' => 
    array (
      'name' => 'Iceland Krona',
      'code' => 352,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'kr',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'JMD' => 
    array (
      'name' => 'Jamaican Dollar',
      'code' => 388,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'JOD' => 
    array (
      'name' => 'Jordanian Dinar',
      'code' => 400,
      'precision' => 3,
      'subunit' => 100,
      'symbol' => 'د.ا',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'JPY' => 
    array (
      'name' => 'Yen',
      'code' => 392,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => '¥',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KES' => 
    array (
      'name' => 'Kenyan Shilling',
      'code' => 404,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'KSh',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KGS' => 
    array (
      'name' => 'Som',
      'code' => 417,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'som',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KHR' => 
    array (
      'name' => 'Riel',
      'code' => 116,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '៛',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KMF' => 
    array (
      'name' => 'Comoro Franc',
      'code' => 174,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KPW' => 
    array (
      'name' => 'North Korean Won',
      'code' => 408,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₩',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KRW' => 
    array (
      'name' => 'Won',
      'code' => 410,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => '₩',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KWD' => 
    array (
      'name' => 'Kuwaiti Dinar',
      'code' => 414,
      'precision' => 3,
      'subunit' => 1000,
      'symbol' => 'د.ك',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KYD' => 
    array (
      'name' => 'Cayman Islands Dollar',
      'code' => 136,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'KZT' => 
    array (
      'name' => 'Tenge',
      'code' => 398,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '〒',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LAK' => 
    array (
      'name' => 'Kip',
      'code' => 418,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₭',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LBP' => 
    array (
      'name' => 'Lebanese Pound',
      'code' => 422,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ل.ل',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LKR' => 
    array (
      'name' => 'Sri Lanka Rupee',
      'code' => 144,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₨',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LRD' => 
    array (
      'name' => 'Liberian Dollar',
      'code' => 430,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LSL' => 
    array (
      'name' => 'Loti',
      'code' => 426,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'L',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LTL' => 
    array (
      'name' => 'Lithuanian Litas',
      'code' => 440,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Lt',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LVL' => 
    array (
      'name' => 'Latvian Lats',
      'code' => 428,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Ls',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'LYD' => 
    array (
      'name' => 'Libyan Dinar',
      'code' => 434,
      'precision' => 3,
      'subunit' => 1000,
      'symbol' => 'ل.د',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MAD' => 
    array (
      'name' => 'Moroccan Dirham',
      'code' => 504,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'د.م.',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MDL' => 
    array (
      'name' => 'Moldovan Leu',
      'code' => 498,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'L',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MGA' => 
    array (
      'name' => 'Malagasy Ariary',
      'code' => 969,
      'precision' => 2,
      'subunit' => 5,
      'symbol' => 'Ar',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MKD' => 
    array (
      'name' => 'Denar',
      'code' => 807,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ден',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MMK' => 
    array (
      'name' => 'Kyat',
      'code' => 104,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'K',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MNT' => 
    array (
      'name' => 'Tugrik',
      'code' => 496,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₮',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MOP' => 
    array (
      'name' => 'Pataca',
      'code' => 446,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'P',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MRO' => 
    array (
      'name' => 'Ouguiya',
      'code' => 478,
      'precision' => 2,
      'subunit' => 5,
      'symbol' => 'UM',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MUR' => 
    array (
      'name' => 'Mauritius Rupee',
      'code' => 480,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₨',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MVR' => 
    array (
      'name' => 'Rufiyaa',
      'code' => 462,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'MVR',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MWK' => 
    array (
      'name' => 'Kwacha',
      'code' => 454,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'MK',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MXN' => 
    array (
      'name' => 'Mexican Peso',
      'code' => 484,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MYR' => 
    array (
      'name' => 'Malaysian Ringgit',
      'code' => 458,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'RM',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'MZN' => 
    array (
      'name' => 'Mozambique Metical',
      'code' => 943,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'MTn',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'NAD' => 
    array (
      'name' => 'Namibia Dollar',
      'code' => 516,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'NGN' => 
    array (
      'name' => 'Naira',
      'code' => 566,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₦',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'NIO' => 
    array (
      'name' => 'Cordoba Oro',
      'code' => 558,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'C$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'NOK' => 
    array (
      'name' => 'Norwegian Krone',
      'code' => 578,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'kr',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'NPR' => 
    array (
      'name' => 'Nepalese Rupee',
      'code' => 524,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₨',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'NZD' => 
    array (
      'name' => 'New Zealand Dollar',
      'code' => 554,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'OMR' => 
    array (
      'name' => 'Rial Omani',
      'code' => 512,
      'precision' => 3,
      'subunit' => 1000,
      'symbol' => 'ر.ع.',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'PAB' => 
    array (
      'name' => 'Balboa',
      'code' => 590,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'B/.',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'PEN' => 
    array (
      'name' => 'Sol',
      'code' => 604,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'S/',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'PGK' => 
    array (
      'name' => 'Kina',
      'code' => 598,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'K',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'PHP' => 
    array (
      'name' => 'Philippine Peso',
      'code' => 608,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₱',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'PKR' => 
    array (
      'name' => 'Pakistan Rupee',
      'code' => 586,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₨',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'PLN' => 
    array (
      'name' => 'Zloty',
      'code' => 985,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'zł',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => ' ',
    ),
    'PYG' => 
    array (
      'name' => 'Guarani',
      'code' => 600,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => '₲',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'QAR' => 
    array (
      'name' => 'Qatari Rial',
      'code' => 634,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ر.ق',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'RON' => 
    array (
      'name' => 'New Romanian Leu',
      'code' => 946,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Lei',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'RSD' => 
    array (
      'name' => 'Serbian Dinar',
      'code' => 941,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'РСД',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'RUB' => 
    array (
      'name' => 'Russian Ruble',
      'code' => 643,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₽',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'RWF' => 
    array (
      'name' => 'Rwanda Franc',
      'code' => 646,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'FRw',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SAR' => 
    array (
      'name' => 'Saudi Riyal',
      'code' => 682,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ر.س',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SBD' => 
    array (
      'name' => 'Solomon Islands Dollar',
      'code' => 90,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SCR' => 
    array (
      'name' => 'Seychelles Rupee',
      'code' => 690,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₨',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SDG' => 
    array (
      'name' => 'Sudanese Pound',
      'code' => 938,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SEK' => 
    array (
      'name' => 'Swedish Krona',
      'code' => 752,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'kr',
      'symbol_first' => false,
      'decimal_mark' => ',',
      'thousands_separator' => ' ',
    ),
    'SGD' => 
    array (
      'name' => 'Singapore Dollar',
      'code' => 702,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SHP' => 
    array (
      'name' => 'Saint Helena Pound',
      'code' => 654,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SLL' => 
    array (
      'name' => 'Leone',
      'code' => 694,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Le',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SOS' => 
    array (
      'name' => 'Somali Shilling',
      'code' => 706,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Sh',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SRD' => 
    array (
      'name' => 'Surinam Dollar',
      'code' => 968,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SSP' => 
    array (
      'name' => 'South Sudanese Pound',
      'code' => 728,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'STD' => 
    array (
      'name' => 'Dobra',
      'code' => 678,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Db',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SVC' => 
    array (
      'name' => 'El Salvador Colon',
      'code' => 222,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₡',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SYP' => 
    array (
      'name' => 'Syrian Pound',
      'code' => 760,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '£S',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'SZL' => 
    array (
      'name' => 'Lilangeni',
      'code' => 748,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'E',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'THB' => 
    array (
      'name' => 'Baht',
      'code' => 764,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '฿',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TJS' => 
    array (
      'name' => 'Somoni',
      'code' => 972,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ЅМ',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TMT' => 
    array (
      'name' => 'Turkmenistan New Manat',
      'code' => 934,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'T',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TND' => 
    array (
      'name' => 'Tunisian Dinar',
      'code' => 788,
      'precision' => 3,
      'subunit' => 1000,
      'symbol' => 'د.ت',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TOP' => 
    array (
      'name' => 'Pa’anga',
      'code' => 776,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'T$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TRY' => 
    array (
      'name' => 'Turkish Lira',
      'code' => 949,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₺',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'TTD' => 
    array (
      'name' => 'Trinidad and Tobago Dollar',
      'code' => 780,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TWD' => 
    array (
      'name' => 'New Taiwan Dollar',
      'code' => 901,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'TZS' => 
    array (
      'name' => 'Tanzanian Shilling',
      'code' => 834,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Sh',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'UAH' => 
    array (
      'name' => 'Hryvnia',
      'code' => 980,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '₴',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'UGX' => 
    array (
      'name' => 'Uganda Shilling',
      'code' => 800,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'USh',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'USD' => 
    array (
      'name' => 'US Dollar',
      'code' => 840,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'UYU' => 
    array (
      'name' => 'Peso Uruguayo',
      'code' => 858,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'UZS' => 
    array (
      'name' => 'Uzbekistan Sum',
      'code' => 860,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'лв',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'VEF' => 
    array (
      'name' => 'Bolivar',
      'code' => 937,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'Bs F',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'VND' => 
    array (
      'name' => 'Dong',
      'code' => 704,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => '₫',
      'symbol_first' => true,
      'decimal_mark' => ',',
      'thousands_separator' => '.',
    ),
    'VUV' => 
    array (
      'name' => 'Vatu',
      'code' => 548,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Vt',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'WST' => 
    array (
      'name' => 'Tala',
      'code' => 882,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'T',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XAF' => 
    array (
      'name' => 'CFA Franc BEAC',
      'code' => 950,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XAG' => 
    array (
      'name' => 'Silver',
      'code' => 961,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'oz t',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XAU' => 
    array (
      'name' => 'Gold',
      'code' => 959,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'oz t',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XCD' => 
    array (
      'name' => 'East Caribbean Dollar',
      'code' => 951,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XDR' => 
    array (
      'name' => 'SDR (Special Drawing Right)',
      'code' => 960,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'SDR',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XOF' => 
    array (
      'name' => 'CFA Franc BCEAO',
      'code' => 952,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'XPF' => 
    array (
      'name' => 'CFP Franc',
      'code' => 953,
      'precision' => 0,
      'subunit' => 1,
      'symbol' => 'Fr',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'YER' => 
    array (
      'name' => 'Yemeni Rial',
      'code' => 886,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '﷼',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ZAR' => 
    array (
      'name' => 'Rand',
      'code' => 710,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'R',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ZMW' => 
    array (
      'name' => 'Zambian Kwacha',
      'code' => 967,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => 'ZK',
      'symbol_first' => false,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
    'ZWL' => 
    array (
      'name' => 'Zimbabwe Dollar',
      'code' => 932,
      'precision' => 2,
      'subunit' => 100,
      'symbol' => '$',
      'symbol_first' => true,
      'decimal_mark' => '.',
      'thousands_separator' => ',',
    ),
  ),
  'permission' => 
  array (
    'models' => 
    array (
      'permission' => 'Spatie\\Permission\\Models\\Permission',
      'role' => 'Spatie\\Permission\\Models\\Role',
    ),
    'table_names' => 
    array (
      'roles' => 'roles',
      'permissions' => 'permissions',
      'model_has_permissions' => 'model_has_permissions',
      'model_has_roles' => 'model_has_roles',
      'role_has_permissions' => 'role_has_permissions',
    ),
    'column_names' => 
    array (
      'role_pivot_key' => NULL,
      'permission_pivot_key' => NULL,
      'model_morph_key' => 'model_id',
      'team_foreign_key' => 'team_id',
    ),
    'register_permission_check_method' => true,
    'teams' => false,
    'display_permission_in_exception' => false,
    'display_role_in_exception' => false,
    'enable_wildcard_permission' => false,
    'cache' => 
    array (
      'expiration_time' => 
      DateInterval::__set_state(array(
         'y' => 0,
         'm' => 0,
         'd' => 0,
         'h' => 24,
         'i' => 0,
         's' => 0,
         'f' => 0.0,
         'weekday' => 0,
         'weekday_behavior' => 0,
         'first_last_day_of' => 0,
         'invert' => 0,
         'days' => false,
         'special_type' => 0,
         'special_amount' => 0,
         'have_weekday_relative' => 0,
         'have_special_relative' => 0,
      )),
      'key' => 'spatie.permission.cache',
      'store' => 'default',
    ),
  ),
  'queue' => 
  array (
    'default' => 'sync',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'verify_csrf_token' => 'App\\Http\\Middleware\\VerifyCsrfToken',
      'encrypt_cookies' => 'App\\Http\\Middleware\\EncryptCookies',
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
      'scheme' => 'https',
    ),
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'google' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => '',
    ),
    'facebook' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => '',
      'app_id' => '',
      'app_secret' => '',
      'config_id' => '',
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '120',
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\xampp\\htdocs\\zaptra\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'zaptra_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
  ),
  'settings' => 
  array (
    'landing_page' => 'Modules\\Wpbox\\Http\\Controllers\\DashboardController',
    'company_page' => '',
    'logo' => '/uploads/settings/c2b98aca-0ed0-403b-94c6-3a7d6bb548ce_logo.jpg',
    'front_languages' => 'EN,English',
    'landing_page_functions' => 'feature,testimonial,faq,mainfeature',
    'landing_page_titles' => 'Features,Testimonials,FAQs,Main Features',
    'app_locale' => 'en',
    'show_demo_credentials' => false,
    'is_demo' => false,
    'enable_pricing' => true,
    'pricing_enabled' => true,
    'subscription_processor' => 'Stripe',
    'local_transfer_info' => 'Wire us the plan amount on the following bank account. And inform us about the wire.',
    'local_transfer_account' => 'IBAN: **************',
    'free_pricing_id' => '1',
    'ignore_subdomains' => 
    array (
      0 => 'demo',
    ),
    'hide_company_profile' => true,
    'vendor_entity_name' => 'Company',
    'vendor_entity_name_plural' => 'Companies',
    'url_route' => 'company',
    'url_route_plural' => 'companies',
    'wildcard_domain_ready' => false,
    'cashier_currency' => 'INR',
    'site_currency' => 'INR',
    'currency' => 'INR',
    'convert' => true,
    'share_this_property' => '',
    'app_code_name' => 'wpbox',
    'apps_available' => '',
    'admin_companies_enabled' => true,
    'stripe_secret' => 'sk_test_XXXXXXXXXXXXXXX',
    'stripe_key' => 'pk_test_XXXXXXXXXXXXXX',
    'ownerAdmin' => 'default',
    'model_for_payment' => 'none',
    'show_company_page' => false,
    'show_company_logo' => false,
    'do_convertion' => true,
    'enable_login_as_client' => false,
    'enable_login_as_company' => true,
    'hide_share_link' => true,
    'limit_items_name' => 'Campaigns limit',
    'limit_views_name' => 'Messages limit',
    'limit_orders_name' => 'Contacts limit',
    'limit_items_show' => true,
    'limit_views_show' => true,
    'limit_orders_show' => true,
    'demo_val1' => NULL,
    'demo_val2' => NULL,
    'demo_val3' => NULL,
    'demo_val4' => NULL,
    'task_1' => 'Setup SMTP - used for sending emails',
    'task_1_docs' => 'https://mobidonia.notion.site/Mail-server-Required-59f5add2a79e41b38a11a85a6735901c?pvs=4',
    'task_1_info' => '',
    'task_2' => 'Setup Pusher - used for live chat',
    'task_2_docs' => 'https://www.notion.so/mobidonia/Pusher-Setup-Required-6de563c4d7344343b57ebd015181415e',
    'task_2_info' => '',
    'task_3' => 'CRON Jobs - used for automated campaigns send',
    'task_3_docs' => 'https://mobidonia.notion.site/Cron-job-setup-Required-c1214d6cb61e431a85d1093b6f8ccec9?pvs=4',
    'task_3_info' => 'Your cron job url is: {url}/webhook/wpbox/sendschuduledmessages',
    'task_4' => NULL,
    'task_4_docs' => NULL,
    'task_4_info' => '',
    'task_5' => NULL,
    'task_5_docs' => NULL,
    'task_5_info' => '',
    'task_6' => NULL,
    'task_6_docs' => NULL,
    'task_6_info' => '',
    'paginate' => '10',
    'use_s3_as_storage' => false,
    'disable_landing_page' => false,
    'hideApps' => false,
    'forceUserToPay' => false,
    'document_repository' => 'Modules\\Privacygen\\Models\\Document',
    'apps_link' => 'https://gist.githubusercontent.com/dimovdaniel/b1621923f8bb30327a6a53a7d6562216/raw/apps.json',
    'enable_credits' => true,
    'enable_multi_organizations' => true,
    'icon_type' => 'nucleo',
    'icon_size' => '24px',
  ),
  'telescope' => 
  array (
    'domain' => NULL,
    'path' => 'telescope',
    'driver' => 'database',
    'storage' => 
    array (
      'database' => 
      array (
        'connection' => 'mysql',
        'chunk' => 1000,
      ),
    ),
    'enabled' => false,
    'middleware' => 
    array (
      0 => 'web',
      1 => 'Laravel\\Telescope\\Http\\Middleware\\Authorize',
    ),
    'only_paths' => 
    array (
    ),
    'ignore_paths' => 
    array (
      0 => 'nova-api*',
    ),
    'ignore_commands' => 
    array (
    ),
    'watchers' => 
    array (
      'Laravel\\Telescope\\Watchers\\BatchWatcher' => true,
      'Laravel\\Telescope\\Watchers\\CacheWatcher' => 
      array (
        'enabled' => true,
        'hidden' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ClientRequestWatcher' => true,
      'Laravel\\Telescope\\Watchers\\CommandWatcher' => 
      array (
        'enabled' => true,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\DumpWatcher' => 
      array (
        'enabled' => true,
        'always' => false,
      ),
      'Laravel\\Telescope\\Watchers\\EventWatcher' => 
      array (
        'enabled' => true,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ExceptionWatcher' => true,
      'Laravel\\Telescope\\Watchers\\GateWatcher' => 
      array (
        'enabled' => true,
        'ignore_abilities' => 
        array (
        ),
        'ignore_packages' => true,
        'ignore_paths' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\JobWatcher' => true,
      'Laravel\\Telescope\\Watchers\\LogWatcher' => 
      array (
        'enabled' => true,
        'level' => 'error',
      ),
      'Laravel\\Telescope\\Watchers\\MailWatcher' => true,
      'Laravel\\Telescope\\Watchers\\ModelWatcher' => 
      array (
        'enabled' => true,
        'events' => 
        array (
          0 => 'eloquent.*',
        ),
        'hydrations' => true,
      ),
      'Laravel\\Telescope\\Watchers\\NotificationWatcher' => true,
      'Laravel\\Telescope\\Watchers\\QueryWatcher' => 
      array (
        'enabled' => true,
        'ignore_packages' => true,
        'ignore_paths' => 
        array (
        ),
        'slow' => 100,
      ),
      'Laravel\\Telescope\\Watchers\\RedisWatcher' => true,
      'Laravel\\Telescope\\Watchers\\RequestWatcher' => 
      array (
        'enabled' => true,
        'size_limit' => 64,
        'ignore_http_methods' => 
        array (
        ),
        'ignore_status_codes' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ScheduleWatcher' => true,
      'Laravel\\Telescope\\Watchers\\ViewWatcher' => true,
    ),
  ),
  'timezones' => 
  array (
    'Pacific/Midway' => '(UTC-11:00) Midway Island',
    'Pacific/Samoa' => '(UTC-11:00) Samoa',
    'Pacific/Honolulu' => '(UTC-10:00) Hawaii',
    'US/Alaska' => '(UTC-09:00) Alaska',
    'America/Los_Angeles' => '(UTC-08:00) Pacific Time (US &amp; Canada)',
    'America/Tijuana' => '(UTC-08:00) Tijuana',
    'US/Arizona' => '(UTC-07:00) Arizona',
    'America/Chihuahua' => '(UTC-07:00) La Paz',
    'America/Mazatlan' => '(UTC-07:00) Mazatlan',
    'US/Mountain' => '(UTC-07:00) Mountain Time (US &amp; Canada)',
    'America/Managua' => '(UTC-06:00) Central America',
    'US/Central' => '(UTC-06:00) Central Time (US &amp; Canada)',
    'America/Mexico_City' => '(UTC-06:00) Mexico City',
    'America/Monterrey' => '(UTC-06:00) Monterrey',
    'Canada/Saskatchewan' => '(UTC-06:00) Saskatchewan',
    'America/Bogota' => '(UTC-05:00) Quito',
    'US/Eastern' => '(UTC-05:00) Eastern Time (US &amp; Canada)',
    'US/East-Indiana' => '(UTC-05:00) Indiana (East)',
    'America/Lima' => '(UTC-05:00) Lima',
    'Canada/Atlantic' => '(UTC-04:00) Atlantic Time (Canada)',
    'America/Caracas' => '(UTC-04:30) Caracas',
    'America/La_Paz' => '(UTC-04:00) La Paz',
    'America/Santiago' => '(UTC-04:00) Santiago',
    'Canada/Newfoundland' => '(UTC-03:30) Newfoundland',
    'America/Sao_Paulo' => '(UTC-03:00) Brasilia',
    'America/Argentina/Buenos_Aires' => '(UTC-03:00) Georgetown',
    'America/Godthab' => '(UTC-03:00) Greenland',
    'America/Noronha' => '(UTC-02:00) Mid-Atlantic',
    'Atlantic/Azores' => '(UTC-01:00) Azores',
    'Atlantic/Cape_Verde' => '(UTC-01:00) Cape Verde Is.',
    'Africa/Casablanca' => '(UTC+00:00) Casablanca',
    'Europe/London' => '(UTC+00:00) London',
    'Etc/Greenwich' => '(UTC+00:00) Greenwich Mean Time : Dublin',
    'Europe/Lisbon' => '(UTC+00:00) Lisbon',
    'Africa/Monrovia' => '(UTC+00:00) Monrovia',
    'UTC' => '(UTC+00:00) UTC',
    'Europe/Amsterdam' => '(UTC+01:00) Amsterdam',
    'Europe/Belgrade' => '(UTC+01:00) Belgrade',
    'Europe/Berlin' => '(UTC+01:00) Berlin',
    'Europe/Bern' => '(UTC+01:00) Bern',
    'Europe/Bratislava' => '(UTC+01:00) Bratislava',
    'Europe/Brussels' => '(UTC+01:00) Brussels',
    'Europe/Budapest' => '(UTC+01:00) Budapest',
    'Europe/Copenhagen' => '(UTC+01:00) Copenhagen',
    'Europe/Ljubljana' => '(UTC+01:00) Ljubljana',
    'Europe/Madrid' => '(UTC+01:00) Madrid',
    'Europe/Paris' => '(UTC+01:00) Paris',
    'Europe/Prague' => '(UTC+01:00) Prague',
    'Europe/Rome' => '(UTC+01:00) Rome',
    'Europe/Sarajevo' => '(UTC+01:00) Sarajevo',
    'Europe/Skopje' => '(UTC+01:00) Skopje',
    'Europe/Stockholm' => '(UTC+01:00) Stockholm',
    'Europe/Vienna' => '(UTC+01:00) Vienna',
    'Europe/Warsaw' => '(UTC+01:00) Warsaw',
    'Africa/Lagos' => '(UTC+01:00) West Central Africa',
    'Europe/Zagreb' => '(UTC+01:00) Zagreb',
    'Europe/Athens' => '(UTC+02:00) Athens',
    'Europe/Bucharest' => '(UTC+02:00) Bucharest',
    'Africa/Cairo' => '(UTC+02:00) Cairo',
    'Africa/Harare' => '(UTC+02:00) Harare',
    'Europe/Helsinki' => '(UTC+02:00) Kyiv',
    'Europe/Istanbul' => '(UTC+02:00) Istanbul',
    'Asia/Jerusalem' => '(UTC+02:00) Jerusalem',
    'Africa/Johannesburg' => '(UTC+02:00) Pretoria',
    'Europe/Riga' => '(UTC+02:00) Riga',
    'Europe/Sofia' => '(UTC+02:00) Sofia',
    'Europe/Tallinn' => '(UTC+02:00) Tallinn',
    'Europe/Vilnius' => '(UTC+02:00) Vilnius',
    'Asia/Baghdad' => '(UTC+03:00) Baghdad',
    'Asia/Kuwait' => '(UTC+03:00) Kuwait',
    'Europe/Minsk' => '(UTC+03:00) Minsk',
    'Africa/Nairobi' => '(UTC+03:00) Nairobi',
    'Asia/Riyadh' => '(UTC+03:00) Riyadh',
    'Europe/Volgograd' => '(UTC+03:00) Volgograd',
    'Asia/Tehran' => '(UTC+03:30) Tehran',
    'Asia/Muscat' => '(UTC+04:00) Muscat',
    'Asia/Baku' => '(UTC+04:00) Baku',
    'Europe/Moscow' => '(UTC+04:00) St. Petersburg',
    'Asia/Tbilisi' => '(UTC+04:00) Tbilisi',
    'Asia/Yerevan' => '(UTC+04:00) Yerevan',
    'Asia/Kabul' => '(UTC+04:30) Kabul',
    'Asia/Karachi' => '(UTC+05:00) Karachi',
    'Asia/Tashkent' => '(UTC+05:00) Tashkent',
    'Asia/Calcutta' => '(UTC+05:30) Sri Jayawardenepura',
    'Asia/Kolkata' => '(UTC+05:30) Kolkata',
    'Asia/Katmandu' => '(UTC+05:45) Kathmandu',
    'Asia/Almaty' => '(UTC+06:00) Almaty',
    'Asia/Dhaka' => '(UTC+06:00) Dhaka',
    'Asia/Yekaterinburg' => '(UTC+06:00) Ekaterinburg',
    'Asia/Rangoon' => '(UTC+06:30) Rangoon',
    'Asia/Bangkok' => '(UTC+07:00) Hanoi',
    'Asia/Jakarta' => '(UTC+07:00) Jakarta',
    'Asia/Novosibirsk' => '(UTC+07:00) Novosibirsk',
    'Asia/Hong_Kong' => '(UTC+08:00) Hong Kong',
    'Asia/Chongqing' => '(UTC+08:00) Chongqing',
    'Asia/Krasnoyarsk' => '(UTC+08:00) Krasnoyarsk',
    'Asia/Kuala_Lumpur' => '(UTC+08:00) Kuala Lumpur',
    'Australia/Perth' => '(UTC+08:00) Perth',
    'Asia/Singapore' => '(UTC+08:00) Singapore',
    'Asia/Taipei' => '(UTC+08:00) Taipei',
    'Asia/Ulan_Bator' => '(UTC+08:00) Ulaan Bataar',
    'Asia/Urumqi' => '(UTC+08:00) Urumqi',
    'Asia/Irkutsk' => '(UTC+09:00) Irkutsk',
    'Asia/Tokyo' => '(UTC+09:00) Tokyo',
    'Asia/Seoul' => '(UTC+09:00) Seoul',
    'Australia/Adelaide' => '(UTC+09:30) Adelaide',
    'Australia/Darwin' => '(UTC+09:30) Darwin',
    'Australia/Brisbane' => '(UTC+10:00) Brisbane',
    'Australia/Canberra' => '(UTC+10:00) Canberra',
    'Pacific/Guam' => '(UTC+10:00) Guam',
    'Australia/Hobart' => '(UTC+10:00) Hobart',
    'Australia/Melbourne' => '(UTC+10:00) Melbourne',
    'Pacific/Port_Moresby' => '(UTC+10:00) Port Moresby',
    'Australia/Sydney' => '(UTC+10:00) Sydney',
    'Asia/Yakutsk' => '(UTC+10:00) Yakutsk',
    'Asia/Vladivostok' => '(UTC+11:00) Vladivostok',
    'Pacific/Auckland' => '(UTC+12:00) Wellington',
    'Pacific/Fiji' => '(UTC+12:00) Marshall Is.',
    'Pacific/Kwajalein' => '(UTC+12:00) International Date Line West',
    'Asia/Kamchatka' => '(UTC+12:00) Kamchatka',
    'Asia/Magadan' => '(UTC+12:00) Solomon Is.',
    'Pacific/Tongatapu' => '(UTC+13:00) Nuku\'alofa',
  ),
  'translation' => 
  array (
    'driver' => 'file',
    'route_group_config' => 
    array (
      'middleware' => 
      array (
        0 => 'web',
        1 => 'isAdmin',
      ),
    ),
    'translation_methods' => 
    array (
      0 => 'trans',
      1 => '__',
    ),
    'scan_paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\zaptra\\app',
      1 => 'C:\\xampp\\htdocs\\zaptra\\resources',
      2 => 'C:\\xampp\\htdocs\\zaptra\\app../modules',
    ),
    'ui_url' => 'tools/languages',
    'database' => 
    array (
      'connection' => '',
      'languages_table' => 'languages',
      'translations_table' => 'translations',
    ),
  ),
  'version' => 
  array (
    'version' => '3.7.0',
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\zaptra\\resources\\views',
    ),
    'compiled' => 'C:\\xampp\\htdocs\\zaptra\\storage\\framework\\views',
  ),
  'blade-heroicons' => 
  array (
    'prefix' => 'heroicon',
    'fallback' => '',
    'class' => '',
    'attributes' => 
    array (
    ),
  ),
  'blade-icons' => 
  array (
    'sets' => 
    array (
    ),
    'class' => '',
    'attributes' => 
    array (
    ),
    'fallback' => '',
    'components' => 
    array (
      'disabled' => false,
      'default' => 'icon',
    ),
  ),
  'filament' => 
  array (
    'broadcasting' => 
    array (
    ),
    'default_filesystem_disk' => 'public',
    'assets_path' => NULL,
    'cache_path' => 'C:\\xampp\\htdocs\\zaptra\\bootstrap/cache/filament',
    'livewire_loading_delay' => 'default',
    'system_route_prefix' => 'filament',
  ),
  'image' => 
  array (
    'driver' => 'gd',
  ),
  'cashier' => 
  array (
    'key' => 'pk_test_XXXXXXXXXXXXXX',
    'secret' => 'sk_test_XXXXXXXXXXXXXXX',
    'path' => 'stripe',
    'webhook' => 
    array (
      'secret' => '',
      'tolerance' => 300,
      'events' => 
      array (
        0 => 'customer.subscription.created',
        1 => 'customer.subscription.updated',
        2 => 'customer.subscription.deleted',
        3 => 'customer.updated',
        4 => 'customer.deleted',
        5 => 'payment_method.automatically_updated',
        6 => 'invoice.payment_action_required',
        7 => 'invoice.payment_succeeded',
      ),
    ),
    'currency' => 'INR',
    'currency_locale' => 'en',
    'payment_notification' => NULL,
    'invoices' => 
    array (
      'renderer' => 'Laravel\\Cashier\\Invoices\\DompdfInvoiceRenderer',
      'options' => 
      array (
        'paper' => 'letter',
      ),
    ),
    'logger' => NULL,
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
        'test_auto_detect' => true,
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => false,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'guess',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
      'cells' => 
      array (
        'middleware' => 
        array (
        ),
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\xampp\\htdocs\\zaptra\\storage\\framework/cache/laravel-excel',
      'local_permissions' => 
      array (
      ),
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => NULL,
    ),
  ),
  'notify' => 
  array (
    'theme' => 'light',
    'timeout' => 5000,
    'preset-messages' => 
    array (
      'user-updated' => 
      array (
        'message' => 'The user has been updated successfully.',
        'type' => 'success',
        'model' => 'connect',
        'title' => 'User Updated',
      ),
    ),
  ),
  'eloquent-sortable' => 
  array (
    'order_column_name' => 'order_column',
    'sort_when_creating' => true,
    'ignore_timestamps' => false,
  ),
  'flare' => 
  array (
    'key' => NULL,
    'flare_middleware' => 
    array (
      0 => 'Spatie\\FlareClient\\FlareMiddleware\\RemoveRequestIp',
      1 => 'Spatie\\FlareClient\\FlareMiddleware\\AddGitInformation',
      2 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddNotifierName',
      3 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddEnvironmentInformation',
      4 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionInformation',
      5 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddDumps',
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs' => 
      array (
        'maximum_number_of_collected_logs' => 200,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries' => 
      array (
        'maximum_number_of_collected_queries' => 200,
        'report_query_bindings' => true,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs' => 
      array (
        'max_chained_job_reporting_depth' => 5,
      ),
      6 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddContext',
      7 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionHandledStatus',
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields' => 
      array (
        'censor_fields' => 
        array (
          0 => 'password',
          1 => 'password_confirmation',
        ),
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders' => 
      array (
        'headers' => 
        array (
          0 => 'API-KEY',
          1 => 'Authorization',
          2 => 'Cookie',
          3 => 'Set-Cookie',
          4 => 'X-CSRF-TOKEN',
          5 => 'X-XSRF-TOKEN',
        ),
      ),
    ),
    'send_logs_as_events' => true,
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'auto',
    'enable_share_button' => true,
    'register_commands' => false,
    'solution_providers' => 
    array (
      0 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\BadMethodCallSolutionProvider',
      1 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider',
      2 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\UndefinedPropertySolutionProvider',
      3 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\IncorrectValetDbCredentialsSolutionProvider',
      4 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingAppKeySolutionProvider',
      5 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\DefaultDbNameSolutionProvider',
      6 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\TableNotFoundSolutionProvider',
      7 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingImportSolutionProvider',
      8 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\InvalidRouteActionSolutionProvider',
      9 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\ViewNotFoundSolutionProvider',
      10 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\RunningLaravelDuskInProductionProvider',
      11 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingColumnSolutionProvider',
      12 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownValidationSolutionProvider',
      13 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingMixManifestSolutionProvider',
      14 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingViteManifestSolutionProvider',
      15 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingLivewireComponentSolutionProvider',
      16 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UndefinedViewVariableSolutionProvider',
      17 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\GenericLaravelExceptionSolutionProvider',
      18 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\OpenAiSolutionProvider',
      19 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SailNetworkSolutionProvider',
      20 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMysql8CollationSolutionProvider',
      21 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMariadbCollationSolutionProvider',
    ),
    'ignored_solution_providers' => 
    array (
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => 'C:\\xampp\\htdocs\\zaptra',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
    'settings_file_path' => '',
    'recorders' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder',
      1 => 'Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder',
      2 => 'Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder',
      3 => 'Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder',
    ),
    'open_ai_key' => NULL,
    'with_stack_frame_arguments' => true,
    'argument_reducers' => 
    array (
      0 => 'Spatie\\Backtrace\\Arguments\\Reducers\\BaseTypeArgumentReducer',
      1 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ArrayArgumentReducer',
      2 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StdClassArgumentReducer',
      3 => 'Spatie\\Backtrace\\Arguments\\Reducers\\EnumArgumentReducer',
      4 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ClosureArgumentReducer',
      5 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeArgumentReducer',
      6 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeZoneArgumentReducer',
      7 => 'Spatie\\Backtrace\\Arguments\\Reducers\\SymphonyRequestArgumentReducer',
      8 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\ModelArgumentReducer',
      9 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\CollectionArgumentReducer',
      10 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StringableArgumentReducer',
    ),
  ),
  'agents' => 
  array (
    'name' => 'Agents',
  ),
  'blog' => 
  array (
    'name' => 'Blog',
    'show_featured_image' => true,
    'disqus_shortname' => 'wpbox',
    'tiny_mce_api_key' => 'ncyt1rcvx8ln70emuf8hmfyorf4d25t06176rasxqn5lyxkn',
  ),
  'contacts' => 
  array (
    'name' => 'Contacts',
  ),
  'dashboard' => 
  array (
    'name' => 'Dashboard',
  ),
  'emailwpbox' => 
  array (
    'name' => 'Emailwpbox',
  ),
  'embeddedlogin' => 
  array (
    'name' => 'Embeddedlogin',
    'config_id' => '',
  ),
  'embedwhatsapp' => 
  array (
    'name' => 'Embedwhatsapp',
  ),
  'flowiseai' => 
  array (
    'name' => 'Flowiseai',
  ),
  'flowmaker' => 
  array (
    'name' => 'Flowmaker',
  ),
  'journies' => 
  array (
    'name' => 'Journies',
  ),
  'razorpay-subscribe' => 
  array (
    'name' => 'RazorpaySubscribe',
    'key' => '',
    'secret' => '',
    'webhook_secret' => '',
  ),
  'reminders' => 
  array (
    'name' => 'Reminders',
  ),
  'shopifylist' => 
  array (
    'name' => 'Shopifylist',
  ),
  'smswpbox' => 
  array (
    'name' => 'Smswpbox',
  ),
  'stripeh-subscribe' => 
  array (
    'name' => 'StripehSubscribe',
    'stripe_key' => 'pk_test_XXXXXXXXXXXXXX',
    'stripe_secret_key' => 'sk_test_XXXXXXXXXXXXXXX',
    'stripe_webhook_secret' => '',
  ),
  'themes' => 
  array (
    'name' => 'Themes',
  ),
  'translatechat' => 
  array (
    'name' => 'Translatechat',
  ),
  'voiceflow' => 
  array (
    'name' => 'Voiceflow',
  ),
  'woolist' => 
  array (
    'name' => 'Woolist',
  ),
  'wpbox' => 
  array (
    'name' => 'Wpbox',
    'google_maps_api_key' => 'AIzaSyBvLDmgTRiCbEZ3TcxqEE0M7CjPO6n0Gtc',
    'google_maps_enabled' => true,
    'api_docs' => 'https://documenter.getpostman.com/view/8538142/2s9Ykn8gvj',
    'pp_url' => '/privacy-policy',
    'terms_url' => '/terms-of-service',
    'disclaimer_url' => '',
    'tw' => '',
    'fb' => '',
    'insta' => '',
    'one_signal_app_id' => '',
    'openai_api_key' => '********************************************************************************************************************************************************************',
    'openai_api_key_demo' => '',
    'openai_max_tokens' => '3500',
    'openai_enabled' => true,
    'openai_model' => 'gpt-3.5-turbo',
    'available_languages' => 'English,Spanish,German,Italian,Portuguese,Dutch,French,Japanese,Chinese',
    'campaign_sending_batch' => '100',
    'campaign_sending_type' => 'normal',
    'mobile_app_ios_url' => '',
    'mobile_app_android_url' => '',
    'chat_page_size' => '6',
  ),
  'wpboxlanding' => 
  array (
    'name' => 'Wpboxlanding',
  ),
  'onesignal' => 
  array (
    'app_id' => NULL,
    'rest_api_url' => 'https://api.onesignal.com',
    'rest_api_key' => NULL,
    'user_auth_key' => NULL,
    'guzzle_client_timeout' => 0,
  ),
  'global' => 
  array (
    'modules' => 
    array (
      0 => 'agents',
      1 => 'blog',
      2 => 'contacts',
      3 => 'dashboard',
      4 => 'emailwpbox',
      5 => 'embeddedlogin',
      6 => 'embedwhatsapp',
      7 => 'flowiseai',
      8 => 'flowmaker',
      9 => 'journies',
      10 => 'razorpay',
      11 => 'razorpay-subscribe',
      12 => 'reminders',
      13 => 'shopifylist',
      14 => 'smswpbox',
      15 => 'stripeh-subscribe',
      16 => 'themes',
      17 => 'translatechat',
      18 => 'vendorlinks',
      19 => 'voiceflow',
      20 => 'woolist',
      21 => 'wpbox',
      22 => 'wpboxlanding',
    ),
    'modulesWithDashboardInfo' => 
    array (
      0 => 'dashboard',
      1 => 'wpbox',
    ),
  ),
  'razorpay' => 
  array (
    'name' => 'Razorpay',
    'enabled' => false,
    'useVendor' => false,
    'useAdmin' => true,
    'key' => '',
    'secret' => '',
    'mode' => 'sandbox',
  ),
  'vendorlinks' => 
  array (
    'name' => 'Support',
    'enable' => true,
    'link1name' => '',
    'link2name' => '',
    'link3name' => '',
    'link1link' => '',
    'link2link' => '',
    'link3link' => '',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);

<?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'admin')): ?>
<div class="row">
    <?php if(config('settings.admin_companies_enabled',true)): ?>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">

                <div class="card-body shadow-lg">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Users')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['total_users']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-single-02"></i>
                            </div>
                        </div>

                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2"><i class="fa fa-users"></i>
                            <?php echo e($dashboard['users_this_month']); ?></span>
                        <span class="text-nowrap"><?php echo e(__('this month')); ?></span>
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php if(config('settings.enable_pricing')): ?>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats shadow-lg">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Paying clients')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['total_paying_users']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-success text-white rounded-circle shadow">
                                <i class="ni ni-trophy"></i>
                            </div>
                        </div>

                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2"><i class="fa fa-arrow-up"></i>
                            <?php echo e($dashboard['total_paying_users_this_month']); ?></span>
                        <span class="text-nowrap"><?php echo e(__('this month')); ?></span>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats shadow-lg">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('MRR')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['mrr']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-chart-bar-32"></i>
                            </div>
                        </div>

                    </div>
                    
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats shadow-lg ">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('ARR')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['arr']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-chart-bar-32"></i>
                            </div>
                        </div>

                    </div>
                    
                </div>
            </div>
        </div> 
    <?php else: ?>
        <!-- Payment based on usage -->
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats shadow-lg">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Documents')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['total_docs_np']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-book-bookmark"></i>
                            </div>

                            
                        </div>
                        

                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <?php echo e($dashboard['month_docs_np']); ?></span>
                        <span class="text-nowrap"><?php echo e(__('this month')); ?></span>
                    </p>
                    
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats shadow-lg">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('This month')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['month']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-chart-bar-32"></i>
                            </div>
                        </div>

                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <?php echo e($dashboard['month_docs']); ?></span>
                        <span class="text-nowrap"><?php echo e(__('Documents')); ?></span>
                    </p>
                    
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats shadow-lg">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Total')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($dashboard['total']); ?></span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-chart-bar-32"></i>
                            </div>

                            
                        </div>
                        

                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <?php echo e($dashboard['total_docs']); ?></span>
                        <span class="text-nowrap"><?php echo e(__('Documents')); ?></span>
                    </p>
                    
                </div>
            </div>
        </div>
    <?php endif; ?>
    
</div>
<?php endif; ?>

<?php if(\Spatie\Permission\PermissionServiceProvider::bladeMethodWrapper('hasRole', 'admin')): ?>
<?php $__env->startSection('dashboard_content2'); ?>
<?php if(config('settings.admin_companies_enabled',true)): ?>
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header border-0">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="mb-0"><?php echo e(__('Latest companies')); ?></h3>
                    </div>
                    <div class="col text-right">
                        <a href="<?php echo e(route('admin.companies.index')); ?>"
                            class="btn btn-sm btn-primary"><?php echo e(__('See all')); ?></a>
                    </div>
                </div>
            </div>
            <div class="table-responsive">

                <table class="table align-items-center table-flush">
                    <thead class="thead-light">
                        <tr>
                            <th scope="col"><?php echo e(__('Company')); ?></th>
                            <th scope="col"><?php echo e(__('Creation Date')); ?></th>
                            <th scope="col"><?php echo e(__('Name')); ?></th>
                            <th scope="col"><?php echo e(__('Email')); ?></th>
                            <?php if(config('settings.enable_pricing')): ?>
                                <th scope="col"><?php echo e(__('Plan')); ?></th>
                            <?php endif; ?>
                            <th scope="col"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $dashboard['clients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($client->user): ?>
                                <tr>
                                    <td scope="row">
                                        <a href="<?php echo e(route('admin.companies.edit',$client->id)); ?>"><?php echo e($client->name); ?></a>
                                    </td>
                                    <td><?php echo e($client->created_at->locale(Config::get('app.locale'))->isoFormat('LLLL')); ?></td>
                                    <td>
                                        <?php echo e($client->user->name); ?>

                                    </td>
                                    <td>
                                        <?php echo e($client->user->email); ?>

                                    </td>
                                    <?php if(config('settings.enable_pricing')): ?>
                                    <td>
                                        <?php if(isset($dashboard['plans'])): ?>
                                            <?php if(isset($dashboard['plans'][$client->user->plan_id])): ?>
                                                <?php echo e($dashboard['plans'][$client->user->plan_id]); ?>

                                            <?php endif; ?>
                                        <?php endif; ?>
                                        
                                    </td>
                                    <?php endif; ?>
                                    <td>
                                        <a class="btn btn-sm btn-primary text-white" href="<?php echo e(route('admin.companies.loginas',  $client)); ?>"><?php echo e(__('Login as')); ?></a>
                                        <?php if(config('settings.show_company_page',true)): ?>
                                            <a target="_blank" href="<?php echo e($client->getLinkAttribute()); ?>" class="btn btn-sm btn-success"><?php echo e(__('View it')); ?></a>
                                        <?php endif; ?>
                                        
                                    </td>
                                </tr>
                            <?php endif; ?>
                        
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-xl-4">
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Dashboard\Providers/../Resources/views/dashboard.blade.php ENDPATH**/ ?>
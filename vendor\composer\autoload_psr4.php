<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'willvincent\\Rateable\\' => array($vendorDir . '/willvincent/laravel-rateable/src'),
    'voku\\' => array($vendorDir . '/voku/portable-ascii/src/voku'),
    'phpseclib3\\' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'dacoto\\LaravelWizardInstaller\\' => array($vendorDir . '/dacoto/laravel-wizard-installer/src'),
    'dacoto\\EnvSet\\' => array($vendorDir . '/dacoto/laravel-env-set/src'),
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'TijsVerkoyen\\CssToInlineStyles\\' => array($vendorDir . '/tijsverkoyen/css-to-inline-styles/src'),
    'Termwind\\' => array($vendorDir . '/nunomaduro/termwind/src'),
    'Symfony\\Polyfill\\Uuid\\' => array($vendorDir . '/symfony/polyfill-uuid'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Icu\\' => array($vendorDir . '/symfony/polyfill-intl-icu'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Symfony\\Component\\Uid\\' => array($vendorDir . '/symfony/uid'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Routing\\' => array($vendorDir . '/symfony/routing'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\Mailer\\' => array($vendorDir . '/symfony/mailer'),
    'Symfony\\Component\\HttpKernel\\' => array($vendorDir . '/symfony/http-kernel'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\HtmlSanitizer\\' => array($vendorDir . '/symfony/html-sanitizer'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\ErrorHandler\\' => array($vendorDir . '/symfony/error-handler'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Bridge\\PsrHttpMessage\\' => array($vendorDir . '/symfony/psr-http-message-bridge'),
    'Stripe\\' => array($vendorDir . '/stripe/stripe-php/lib'),
    'Spatie\\WelcomeNotification\\' => array($vendorDir . '/spatie/laravel-welcome-notification/src'),
    'Spatie\\Translatable\\' => array($vendorDir . '/spatie/laravel-translatable/src'),
    'Spatie\\Sluggable\\' => array($vendorDir . '/spatie/laravel-sluggable/src'),
    'Spatie\\Permission\\' => array($vendorDir . '/spatie/laravel-permission/src'),
    'Spatie\\OpeningHours\\' => array($vendorDir . '/spatie/opening-hours/src'),
    'Spatie\\LaravelPackageTools\\' => array($vendorDir . '/spatie/laravel-package-tools/src'),
    'Spatie\\LaravelIgnition\\' => array($vendorDir . '/spatie/laravel-ignition/src', $vendorDir . '/spatie/error-solutions/legacy/laravel-ignition'),
    'Spatie\\Invade\\' => array($vendorDir . '/spatie/invade/src'),
    'Spatie\\Ignition\\' => array($vendorDir . '/spatie/ignition/src', $vendorDir . '/spatie/error-solutions/legacy/ignition'),
    'Spatie\\FlareClient\\' => array($vendorDir . '/spatie/flare-client-php/src'),
    'Spatie\\ErrorSolutions\\' => array($vendorDir . '/spatie/error-solutions/src'),
    'Spatie\\EloquentSortable\\' => array($vendorDir . '/spatie/eloquent-sortable/src'),
    'Spatie\\Color\\' => array($vendorDir . '/spatie/color/src'),
    'Spatie\\Backtrace\\' => array($vendorDir . '/spatie/backtrace/src'),
    'RyanChandler\\BladeCaptureDirective\\Database\\Factories\\' => array($vendorDir . '/ryangjchandler/blade-capture-directive/database/factories'),
    'RyanChandler\\BladeCaptureDirective\\' => array($vendorDir . '/ryangjchandler/blade-capture-directive/src'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Pusher\\' => array($vendorDir . '/pusher/pusher-php-server/src'),
    'Psy\\' => array($vendorDir . '/psy/psysh/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'PragmaRX\\Google2FA\\' => array($vendorDir . '/pragmarx/google2fa/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'ParagonIE\\ConstantTime\\' => array($vendorDir . '/paragonie/constant_time_encoding/src'),
    'OpenSpout\\' => array($vendorDir . '/openspout/openspout/src'),
    'Opcodes\\MailParser\\' => array($vendorDir . '/opcodesio/mail-parser/src'),
    'Opcodes\\LogViewer\\' => array($vendorDir . '/opcodesio/log-viewer/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Money\\' => array($vendorDir . '/moneyphp/money/src'),
    'Modules\\' => array($baseDir . '/modules'),
    'Mckenziearts\\Notify\\' => array($vendorDir . '/mckenziearts/laravel-notify/src'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'Maatwebsite\\Excel\\' => array($vendorDir . '/maatwebsite/excel/src'),
    'Livewire\\' => array($vendorDir . '/livewire/livewire/src'),
    'League\\Uri\\' => array($vendorDir . '/league/uri', $vendorDir . '/league/uri-interfaces'),
    'League\\OAuth1\\Client\\' => array($vendorDir . '/league/oauth1-client/src'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\Local\\' => array($vendorDir . '/league/flysystem-local'),
    'League\\Flysystem\\AwsS3V3\\' => array($vendorDir . '/league/flysystem-aws-s3-v3'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Csv\\' => array($vendorDir . '/league/csv/src'),
    'League\\Config\\' => array($vendorDir . '/league/config/src'),
    'League\\CommonMark\\' => array($vendorDir . '/league/commonmark/src'),
    'Laravel\\Tinker\\' => array($vendorDir . '/laravel/tinker/src'),
    'Laravel\\Telescope\\Database\\Factories\\' => array($vendorDir . '/laravel/telescope/database/factories'),
    'Laravel\\Telescope\\' => array($vendorDir . '/laravel/telescope/src'),
    'Laravel\\Socialite\\' => array($vendorDir . '/laravel/socialite/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Laravel\\Sanctum\\' => array($vendorDir . '/laravel/sanctum/src'),
    'Laravel\\Prompts\\' => array($vendorDir . '/laravel/prompts/src'),
    'Laravel\\Jetstream\\' => array($vendorDir . '/laravel/jetstream/src'),
    'Laravel\\Fortify\\' => array($vendorDir . '/laravel/fortify/src'),
    'Laravel\\Cashier\\Database\\Factories\\' => array($vendorDir . '/laravel/cashier/database/factories'),
    'Laravel\\Cashier\\' => array($vendorDir . '/laravel/cashier/src'),
    'Kirschbaum\\PowerJoins\\' => array($vendorDir . '/kirschbaum-development/eloquent-power-joins/src'),
    'JoeDixon\\Translation\\' => array($vendorDir . '/joedixon/laravel-translation/src'),
    'JmesPath\\' => array($vendorDir . '/mtdowling/jmespath.php/src'),
    'Intervention\\Image\\' => array($vendorDir . '/intervention/image/src/Intervention/Image'),
    'Illuminate\\Support\\' => array($vendorDir . '/laravel/framework/src/Illuminate/Macroable', $vendorDir . '/laravel/framework/src/Illuminate/Collections', $vendorDir . '/laravel/framework/src/Illuminate/Conditionable'),
    'Illuminate\\Database\\Eloquent\\' => array($vendorDir . '/laravel/legacy-factories/src'),
    'Illuminate\\' => array($vendorDir . '/laravel/framework/src/Illuminate'),
    'GuzzleHttp\\UriTemplate\\' => array($vendorDir . '/guzzlehttp/uri-template/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Fruitcake\\Cors\\' => array($vendorDir . '/fruitcake/php-cors/src'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Filament\\Tables\\' => array($vendorDir . '/filament/tables/src'),
    'Filament\\Support\\' => array($vendorDir . '/filament/support/src'),
    'Filament\\Notifications\\' => array($vendorDir . '/filament/notifications/src'),
    'Filament\\Infolists\\' => array($vendorDir . '/filament/infolists/src'),
    'Filament\\Forms\\' => array($vendorDir . '/filament/forms/src'),
    'Filament\\Actions\\' => array($vendorDir . '/filament/actions/src'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/src'),
    'Doctrine\\DBAL\\' => array($vendorDir . '/doctrine/dbal/src'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Doctrine\\Common\\' => array($vendorDir . '/doctrine/event-manager/src'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'Detection\\' => array($vendorDir . '/mobiledetect/mobiledetectlib/src'),
    'Database\\Seeders\\' => array($baseDir . '/database/seeders'),
    'Database\\Factories\\' => array($baseDir . '/database/factories'),
    'DanHarrin\\DateFormatConverter\\' => array($vendorDir . '/danharrin/date-format-converter/src'),
    'DASPRiD\\Enum\\' => array($vendorDir . '/dasprid/enum/src'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Pcre\\' => array($vendorDir . '/composer/pcre/src'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
    'BladeUI\\Icons\\' => array($vendorDir . '/blade-ui-kit/blade-icons/src'),
    'BladeUI\\Heroicons\\' => array($vendorDir . '/blade-ui-kit/blade-heroicons/src'),
    'Berkayk\\OneSignal\\' => array($vendorDir . '/berkayk/onesignal-laravel/src'),
    'BaconQrCode\\' => array($vendorDir . '/bacon/bacon-qr-code/src'),
    'Aws\\' => array($vendorDir . '/aws/aws-sdk-php/src'),
    'App\\' => array($baseDir . '/app', $baseDir . '/modules/Orders'),
    'AnourValar\\EloquentSerialize\\' => array($vendorDir . '/anourvalar/eloquent-serialize/src'),
    'Akaunting\\Money\\' => array($vendorDir . '/akaunting/laravel-money/src'),
    'Akaunting\\Module\\' => array($vendorDir . '/akaunting/laravel-module/src'),
);

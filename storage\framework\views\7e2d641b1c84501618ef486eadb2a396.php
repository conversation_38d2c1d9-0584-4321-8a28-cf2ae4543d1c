<!-- Exrta menus -->
<?php $__currentLoopData = auth()->user()->getExtraMenus(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<?php if(isset($menu['isGroup']) && $menu['isGroup']): ?>

    <a class="nav-link " href="#navbar-<?php echo e($menu['id']); ?>" data-toggle="collapse" role="button" aria-expanded="false" aria-controls="navbar-<?php echo e($menu['id']); ?>">
        <i class="<?php echo e($menu['icon']); ?>"></i>
        <span class="nav-link-text"><?php echo e(__($menu['name'])); ?></span>
    </a>
    <div class="collapse <?php if(Route::currentRouteName() == $menu['route'] || collect($menu['menus'])->pluck('route')->contains(Route::currentRouteName())): ?> show <?php endif; ?>"" id="navbar-<?php echo e($menu['id']); ?>" style="">
        <ul class="nav nav-sm flex-column">
            <?php $__currentLoopData = $menu['menus']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $submenu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="nav-item">
                    <a class="nav-link <?php if(Route::currentRouteName() == $submenu['route']): ?> active <?php endif; ?>" href="<?php echo e(route($submenu['route'],isset($submenu['params'])?$submenu['params']:[])); ?>">
                        <i class="<?php echo e($submenu['icon']); ?>"></i> <?php echo e(__($submenu['name'])); ?>

                    </a>
                </li> 
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php else: ?>
    <li class="nav-item">
        <a  class="nav-link <?php if(Route::currentRouteName() == $menu['route']): ?> active <?php endif; ?>" href="<?php echo e(route($menu['route'],isset($menu['params'])?$menu['params']:[])); ?>"   >
            <?php if(isset($menu['svg'])): ?>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="<?php echo e($menu['color']); ?>" viewBox="0 0 16 16">
                    <path d="<?php echo e($menu['svg']); ?>"/>
                </svg>
                <span style="margin-left: 20px"><?php echo e(__($menu['name'])); ?>  </span>
            <?php else: ?>
                <i class="<?php echo e($menu['icon']); ?>"></i><?php echo e(__($menu['name'])); ?>  
            <?php endif; ?>  
             
            
        </a>
    </li> 
<?php endif; ?>    
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php /**PATH C:\xampp\htdocs\zaptra\resources\views/admin/navbars/menus/extra.blade.php ENDPATH**/ ?>
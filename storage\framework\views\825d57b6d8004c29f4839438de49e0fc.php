<ul class="navbar-nav">
    <li class="nav-item">
        <a class="nav-link <?php if(Route::currentRouteName() == 'dashboard'): ?> active <?php endif; ?>"
            href="<?php echo e(route('dashboard')); ?>">
            <i class="ni ni-tv-2 text-primary"></i> <?php echo e(__('Dashboard')); ?>

        </a>
    </li>

    <?php echo $__env->make('admin.navbars.menus.extra', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if(!config('settings.hide_company_profile',false)): ?>
        <li class="nav-item">
            <a class="nav-link <?php if(Route::currentRouteName() == 'admin.companies.edit'): ?> active <?php endif; ?>"
                href="<?php echo e(route('admin.companies.edit', auth()->user()->currentCompany()->id)); ?>">
                <i class="ni ni-shop text-primary"></i> <?php echo e(__('Company')); ?>

            </a>
        </li>
    <?php endif; ?>

    <?php if(!config('settings.hide_company_apps',false)): ?>
        <li class="nav-item">
            <a class="nav-link <?php if(Route::currentRouteName() == 'admin.apps.company'): ?> active <?php endif; ?>"
                href="<?php echo e(route('admin.apps.company')); ?>">
                <i class="ni ni-spaceship text-red"></i> <?php echo e(__('Apps')); ?>

            </a>
        </li>
    <?php endif; ?>
    
    

   

    <?php if(config('settings.enable_pricing')): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(route('plans.current')); ?>">
                <i class="ni ni-credit-card text-orange"></i> <?php echo e(__('Plan')); ?>

            </a>
        </li>
    <?php endif; ?>

    <?php if(!config('settings.hide_share_link',false)): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(route('admin.share')); ?>">
                <i class="ni ni-send text-green"></i> <?php echo e(__('Share')); ?>

            </a>
        </li>
        
    <?php endif; ?>
    

       
    

</ul>
<?php if(config('vendorlinks.enable',false)): ?>
<hr class="my-3">
<h6 class="navbar-heading p-0 text-muted">
    <span class="docs-normal"><?php echo e(__(config('vendorlinks.name',""))); ?></span>
</h6>
<ul class="navbar-nav mb-md-3">
    <?php if(strlen(config('vendorlinks.link1link',""))>4): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(config('vendorlinks.link1link',"")); ?>" target="_blank">
                <span class="nav-link-text"><?php echo e(__(config('vendorlinks.link1name',""))); ?></span>
            </a>
        </li>
    <?php endif; ?>

    <?php if(strlen(config('vendorlinks.link2link',""))>4): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(config('vendorlinks.link2link',"")); ?>" target="_blank">
                <span class="nav-link-text"><?php echo e(__(config('vendorlinks.link2name',""))); ?></span>
            </a>
        </li>
    <?php endif; ?>

    <?php if(strlen(config('vendorlinks.link3link',""))>4): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo e(config('vendorlinks.link3link',"")); ?>" target="_blank">
                <span class="nav-link-text"><?php echo e(__(config('vendorlinks.link3name',""))); ?></span>
            </a>
        </li>
    <?php endif; ?>
    
</ul>
<?php endif; ?>

<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/admin/navbars/menus/owner.blade.php ENDPATH**/ ?>
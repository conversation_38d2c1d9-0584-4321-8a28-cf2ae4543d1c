<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e(config('app.name','WhatsBox')); ?></title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
   
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo $__env->yieldContent('head'); ?>
    
    <!-- RTL and Commmon ( Phone ) -->
    <?php echo $__env->make('layouts.rtl', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

    <!-- Custom CSS defined by admin -->
    <link type="text/css" href="<?php echo e(asset('byadmin')); ?>/front.css" rel="stylesheet">
    
</head>
<body class="landing-page">
    <?php echo $__env->make('wpboxlanding::landing.partials.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    
    <?php echo $__env->make('wpboxlanding::landing.partials.products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> 
    <?php if(strlen(config('wpbox.mobile_app_android_url',"#")) > 5 || strlen(config('wpbox.mobile_app_ios_url',"#")) > 5): ?>
        <?php echo $__env->make('wpboxlanding::landing.partials.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    <?php echo $__env->make('wpboxlanding::landing.partials.apps', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php if(isset($hasBlog) && $hasBlog): ?>
        <?php echo $__env->make('wpboxlanding::landing.partials.latest_posts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php echo $__env->make('wpboxlanding::landing.partials.testimonials', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('wpboxlanding::landing.partials.pricing', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('wpboxlanding::landing.partials.faq', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('wpboxlanding::landing.partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    

    <!-- AlpineJS Library -->
    <script src="<?php echo e(asset('vendor')); ?>/alpine/alpine.js"></script>
    
    <!--   Core JS Files   -->
    <script src="<?php echo e(asset('vendor')); ?>/jquery/jquery.min.js" type="text/javascript"></script>
 

    <!-- All in one -->
    <script src="<?php echo e(asset('custom')); ?>/js/js.js?id=<?php echo e(config('version.version')); ?>s"></script>

    <!-- Custom JS defined by admin -->
    <?php echo file_get_contents(base_path('public/byadmin/front.js')) ?>

    <script>
        window.onload = function () {
    
        $('#termsCheckBox').on('click',function () {
            $('#submitRegister').prop("disabled", !$("#termsCheckBox").prop("checked"));
            if(this.checked){
                $('#submitRegister').addClass('opacity-100');
            }else{
                $('#submitRegister').removeClass('opacity-100');
                 
            }
           
        })
    }
    </script>

</body>
</html><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpboxlanding\Providers/../Resources/views/landing/index.blade.php ENDPATH**/ ?>
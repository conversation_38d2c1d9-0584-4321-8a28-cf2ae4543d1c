
<?php
    $hasFeature = isset($feature) && is_object($feature) && !empty($feature->title) && !empty($feature->image_link);
?>

<?php if($hasFeature): ?>
<div class="flex mt-10 flex-col-reverse items-center w-full p-10 bg-gray-100 md:rounded-xl sm:p-10 md:flex-row" data-rounded="rounded-xl" data-rounded-max="rounded-full">

    <div class="w-full mt-16 md:w-1/2 md:mt-0">
        <img src="<?php echo e($feature->image_link); ?>" class="w-full" alt="<?php echo e($feature->title); ?>">
    </div>

    <div class="flex flex-col w-full space-y-6 text-center md:w-1/2 px-7 sm:px-0">
        <h2 class="max-w-md mx-auto text-3xl font-semibold md:text-4xl"><?php echo e($feature->title); ?></h2>
        <p class="text-gray-600"><?php echo e($feature->description); ?></p>
    </div>

</div>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpboxlanding\Providers/../Resources/views/landing/partials/fullproduct.blade.php ENDPATH**/ ?>
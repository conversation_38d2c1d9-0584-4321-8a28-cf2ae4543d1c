
<?php $__env->startSection('admin_title'); ?>
    <?php echo e(__('Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="header pb-8 pt-5 pt-md-8">
    <div class="container-fluid">
        <div class="header-body">

            <h1 class="mb-3 mt--3"><?php echo e(__('Welcome back')); ?>, <?php echo e(auth()->user()->name); ?> 👏</h1>
            <?php if(count($tasks)>0): ?>
                <?php echo $__env->make('dashboard::tasks', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php $__currentLoopData = config('global.modulesWithDashboardInfo'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleWithDashboardInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make($moduleWithDashboardInfo.'::dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <?php if(auth()->user()->hasRole('owner') && config('settings.enable_credits')): ?>
                <?php
                    $authUser=auth()->user();
                    $company=auth()->user()->currentCompany();
                    $totalCreditsAndPercentageUsed=$company->getTotalRemainingCreditsAndPercentageUsed();
                    $availableCredits=$totalCreditsAndPercentageUsed[0];
                    $percentageUsed=$totalCreditsAndPercentageUsed[1][0];
                ?>
                <!-- Credits -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Credits</h5>
                                <div class="d-flex align-items-center">
                                    <div style="width: 80px; height: 80px;">
                                        <canvas id="creditsChart"></canvas>
                                    </div>
                                    <div class="ml-3">
                                        <p class="mb-1">Available Credits: <strong><?php echo e($availableCredits); ?></strong></p>
                                        <p class="mb-0">Used: <strong><?php echo e($percentageUsed); ?>%</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                        <script>
                            const ctx = document.getElementById('creditsChart');
                            new Chart(ctx, {
                                type: 'doughnut',
                                data: {
                                    labels: ['Used', 'Available'],
                                    datasets: [{
                                        data: [<?php echo e($percentageUsed); ?>, <?php echo e(100 - $percentageUsed); ?>],
                                        backgroundColor: ['#ff6384', '#36a2eb']
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: true,
                                    plugins: {
                                        legend: {
                                            display: false
                                        }
                                    }
                                }
                            });
                        </script>
                    </div>
                </div>
            <?php endif; ?>

           
        </div>
    </div>
</div>

<div class="container-fluid mt--6">
    <?php echo $__env->yieldContent('dashboard_content'); ?>
    <?php echo $__env->yieldContent('dashboard_content2'); ?>
    <?php echo $__env->yieldContent('dashboard_content3'); ?>
    <?php echo $__env->yieldContent('dashboard_content4'); ?>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\modules\Dashboard\Providers/../Resources/views/index.blade.php ENDPATH**/ ?>
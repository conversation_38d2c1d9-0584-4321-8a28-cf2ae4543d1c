<section id="features" class="w-full bg-white " >
    <div class="pB-10 mx-auto max-w-7xl md:px-8">

        <?php
            // Filter out empty features
            $validFeatures = collect($features)->filter(function($feature) {
                return is_object($feature) && !empty($feature->title) && !empty($feature->image_link);
            })->values();
        ?>

        <?php for($i = 0; $i < count($validFeatures); $i++): ?>
            <?php
                $feature = $validFeatures[$i];
            ?>
            <?php if($i ==0 || $i == 3 || $i == 6 || $i == 9 || (($i+1)>count($validFeatures)-1)): ?>
                <?php echo $__env->make('wpboxlanding::landing.partials.fullproduct', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php else: ?>
                <?php
                    $feature2 = $validFeatures[$i+1];
                    $i++;
                ?>
                <?php echo $__env->make('wpboxlanding::landing.partials.twoproducts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <?php endif; ?>
        <?php endfor; ?>
    </div>
</section><?php /**PATH C:\xampp\htdocs\zaptra\modules\Wpboxlanding\Providers/../Resources/views/landing/partials/products.blade.php ENDPATH**/ ?>